#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_stacking.py
---------------
测试堆叠方法是否正常工作
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, f1_score

# 导入我们修改后的模块
from tree_attention_pipeline import (
    train_xgb, train_cat, train_stacking_ensemble,
    StackingFusionWrapper, SimpleStackingWrapper,
    infer_class_weight
)

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 创建4分类问题
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_classes=4,
        n_informative=15,
        n_redundant=5,
        random_state=42,
        class_sep=0.8
    )
    
    # 划分训练和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"✅ 数据创建完成:")
    print(f"   训练集: {X_train.shape}, 测试集: {X_test.shape}")
    print(f"   类别分布: {dict(zip(*np.unique(y_train, return_counts=True)))}")
    
    return X_train, X_test, y_train, y_test

def test_tree_models(X_train, y_train, cls_w):
    """测试树模型训练"""
    print("\n🌳 测试树模型训练...")
    
    # XGBoost配置
    xgb_config = {
        "n_estimators": 50,
        "max_depth": 6,
        "learning_rate": 0.1,
        "random_state": 42
    }
    
    # CatBoost配置
    cat_config = {
        "iterations": 50,
        "depth": 6,
        "learning_rate": 0.1,
        "random_state": 42,
        "verbose": False
    }
    
    # 训练XGBoost
    print("  训练XGBoost...")
    xgb_model = train_xgb(X_train, y_train, cls_w, xgb_config)
    
    # 训练CatBoost
    print("  训练CatBoost...")
    cat_model = train_cat(X_train, y_train, cls_w, cat_config)
    
    print("✅ 树模型训练完成")
    return xgb_model, cat_model

def test_stacking_ensemble(X_train, y_train, X_test, y_test, xgb_model, cat_model, cls_w):
    """测试堆叠集成"""
    print("\n🎯 测试堆叠集成...")
    
    # 训练堆叠模型
    stacking_model = train_stacking_ensemble(
        X_train, y_train, xgb_model, cat_model,
        sample_weight=None,  # 简化测试
        cv_folds=3,  # 减少折数以加快测试
        random_state=42
    )
    
    # 创建融合包装器
    fusion_wrapper = StackingFusionWrapper(
        xgb_model, cat_model, stacking_model, minority_boost=True
    )
    
    # 测试预测
    print("  测试预测...")
    test_proba = fusion_wrapper.predict_proba(X_test)
    test_pred = fusion_wrapper.predict(X_test)
    
    # 评估性能
    test_f1 = f1_score(y_test, test_pred, average='macro')
    
    print(f"✅ 堆叠集成测试完成:")
    print(f"   测试集 Macro F1-Score: {test_f1:.4f}")
    
    # 详细报告
    print("\n📊 分类报告:")
    print(classification_report(y_test, test_pred, target_names=[f'Class_{i}' for i in range(4)]))
    
    return fusion_wrapper, test_f1

def compare_with_individual_models(X_test, y_test, xgb_model, cat_model, fusion_wrapper):
    """比较堆叠模型与单个模型的性能"""
    print("\n📊 性能比较...")
    
    # XGBoost性能
    xgb_pred = xgb_model.predict(X_test)
    xgb_f1 = f1_score(y_test, xgb_pred, average='macro')
    
    # CatBoost性能
    cat_pred = cat_model.predict(X_test)
    cat_f1 = f1_score(y_test, cat_pred, average='macro')
    
    # 堆叠模型性能
    stack_pred = fusion_wrapper.predict(X_test)
    stack_f1 = f1_score(y_test, stack_pred, average='macro')
    
    print(f"🔍 模型性能对比:")
    print(f"   XGBoost F1:     {xgb_f1:.4f}")
    print(f"   CatBoost F1:    {cat_f1:.4f}")
    print(f"   堆叠模型 F1:    {stack_f1:.4f}")
    
    # 计算提升
    best_individual = max(xgb_f1, cat_f1)
    improvement = stack_f1 - best_individual
    
    if improvement > 0:
        print(f"✅ 堆叠模型提升: +{improvement:.4f} ({improvement/best_individual*100:.1f}%)")
    else:
        print(f"⚠️ 堆叠模型下降: {improvement:.4f} ({improvement/best_individual*100:.1f}%)")
    
    return {
        'xgb_f1': xgb_f1,
        'cat_f1': cat_f1,
        'stack_f1': stack_f1,
        'improvement': improvement
    }

def main():
    """主测试函数"""
    print("🚀 开始堆叠方法测试")
    print("=" * 50)
    
    try:
        # 1. 创建测试数据
        X_train, X_test, y_train, y_test = create_test_data()
        
        # 2. 计算类别权重
        cls_w = infer_class_weight(y_train, "balanced")
        print(f"📊 类别权重: {cls_w}")
        
        # 3. 训练树模型
        xgb_model, cat_model = test_tree_models(X_train, y_train, cls_w)
        
        # 4. 测试堆叠集成
        fusion_wrapper, stack_f1 = test_stacking_ensemble(
            X_train, y_train, X_test, y_test, xgb_model, cat_model, cls_w
        )
        
        # 5. 性能比较
        results = compare_with_individual_models(
            X_test, y_test, xgb_model, cat_model, fusion_wrapper
        )
        
        print("\n🎉 测试完成！")
        print("=" * 50)
        
        # 总结
        if results['improvement'] > 0.01:
            print("✅ 堆叠方法工作正常，性能有显著提升")
        elif results['improvement'] > 0:
            print("✅ 堆叠方法工作正常，性能有轻微提升")
        else:
            print("⚠️ 堆叠方法工作正常，但性能未提升（可能需要调参）")
            
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
