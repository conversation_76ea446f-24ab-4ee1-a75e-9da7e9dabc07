#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_stacking_simple.py
----------------------
简化的堆叠方法测试脚本
"""

import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import f1_score, classification_report

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试导入...")
    
    try:
        from tree_attention_pipeline import (
            train_xgb, train_cat, train_stacking_ensemble,
            StackingFusionWrapper, SimpleStackingWrapper,
            infer_class_weight
        )
        print("✅ 所有导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_simple_test_data():
    """创建简单测试数据"""
    print("🔧 创建测试数据...")
    
    X, y = make_classification(
        n_samples=500,
        n_features=10,
        n_classes=4,
        n_informative=8,
        n_redundant=2,
        random_state=42,
        class_sep=1.0
    )
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"✅ 数据创建完成: 训练集{X_train.shape}, 测试集{X_test.shape}")
    return X_train, X_test, y_train, y_test

def test_tree_models():
    """测试树模型训练"""
    print("\n🌳 测试树模型...")
    
    from tree_attention_pipeline import train_xgb, train_cat, infer_class_weight
    
    X_train, X_test, y_train, y_test = create_simple_test_data()
    
    # 计算类别权重
    cls_w = infer_class_weight(y_train, "balanced")
    
    # 简化的配置
    xgb_config = {
        "n_estimators": 20,
        "max_depth": 4,
        "learning_rate": 0.1,
        "random_state": 42
    }
    
    cat_config = {
        "iterations": 20,
        "depth": 4,
        "learning_rate": 0.1,
        "random_state": 42,
        "verbose": False
    }
    
    # 训练模型
    xgb_model = train_xgb(X_train, y_train, cls_w, xgb_config)
    cat_model = train_cat(X_train, y_train, cls_w, cat_config)
    
    print("✅ 树模型训练成功")
    return X_train, X_test, y_train, y_test, xgb_model, cat_model, cls_w

def test_stacking():
    """测试堆叠方法"""
    print("\n🎯 测试堆叠方法...")
    
    from tree_attention_pipeline import train_stacking_ensemble, StackingFusionWrapper
    
    X_train, X_test, y_train, y_test, xgb_model, cat_model, cls_w = test_tree_models()
    
    # 训练堆叠模型
    stacking_model = train_stacking_ensemble(
        X_train, y_train, xgb_model, cat_model,
        sample_weight=None,
        cv_folds=3,
        random_state=42
    )
    
    # 创建融合包装器
    fusion_wrapper = StackingFusionWrapper(
        xgb_model, cat_model, stacking_model, minority_boost=True
    )
    
    # 测试预测
    test_proba = fusion_wrapper.predict_proba(X_test)
    test_pred = fusion_wrapper.predict(X_test)
    
    # 评估性能
    test_f1 = f1_score(y_test, test_pred, average='macro')
    
    print(f"✅ 堆叠测试完成，F1-Score: {test_f1:.4f}")
    
    # 比较单个模型性能
    xgb_pred = xgb_model.predict(X_test)
    cat_pred = cat_model.predict(X_test)
    
    xgb_f1 = f1_score(y_test, xgb_pred, average='macro')
    cat_f1 = f1_score(y_test, cat_pred, average='macro')
    
    print(f"📊 性能对比:")
    print(f"   XGBoost F1:  {xgb_f1:.4f}")
    print(f"   CatBoost F1: {cat_f1:.4f}")
    print(f"   堆叠模型 F1: {test_f1:.4f}")
    
    best_individual = max(xgb_f1, cat_f1)
    improvement = test_f1 - best_individual
    
    if improvement > 0:
        print(f"✅ 堆叠模型提升: +{improvement:.4f}")
    else:
        print(f"⚠️ 堆叠模型未提升: {improvement:.4f}")
    
    return test_f1 > best_individual

def main():
    """主测试函数"""
    print("🚀 开始简化堆叠测试")
    print("=" * 40)
    
    try:
        # 1. 测试导入
        if not test_imports():
            return False
        
        # 2. 测试堆叠方法
        success = test_stacking()
        
        if success:
            print("\n🎉 测试成功！堆叠方法工作正常")
        else:
            print("\n⚠️ 测试完成，但堆叠方法未显示明显优势")
        
        print("=" * 40)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
