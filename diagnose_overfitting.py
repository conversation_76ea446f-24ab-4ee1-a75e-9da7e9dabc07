#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
diagnose_overfitting.py
----------------------
诊断和解决过拟合问题
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import validation_curve, learning_curve
from sklearn.metrics import f1_score, accuracy_score
import joblib
import os

def analyze_train_test_gap(train_scores, test_scores, metric_name="F1"):
    """分析训练集和测试集的性能差距"""
    print(f"\n📊 {metric_name} 性能差距分析")
    print("=" * 40)
    
    gap = train_scores - test_scores
    gap_ratio = gap / train_scores if train_scores > 0 else 0
    
    print(f"训练集 {metric_name}: {train_scores:.4f}")
    print(f"测试集 {metric_name}: {test_scores:.4f}")
    print(f"绝对差距: {gap:.4f}")
    print(f"相对差距: {gap_ratio:.2%}")
    
    # 过拟合程度判断
    if gap_ratio > 0.15:
        level = "🚨 严重过拟合"
        suggestions = [
            "大幅增加正则化",
            "减少模型复杂度",
            "增加训练数据",
            "使用更强的早停策略"
        ]
    elif gap_ratio > 0.08:
        level = "⚠️ 中度过拟合"
        suggestions = [
            "适度增加正则化",
            "调整模型参数",
            "使用交叉验证"
        ]
    elif gap_ratio > 0.03:
        level = "🟡 轻微过拟合"
        suggestions = [
            "微调正则化参数",
            "监控验证性能"
        ]
    else:
        level = "✅ 拟合良好"
        suggestions = ["保持当前配置"]
    
    print(f"过拟合程度: {level}")
    print(f"建议措施:")
    for suggestion in suggestions:
        print(f"  - {suggestion}")
    
    return {
        'train_score': float(train_scores),
        'test_score': float(test_scores),
        'gap': float(gap),
        'gap_ratio': float(gap_ratio),
        'level': level,
        'suggestions': suggestions
    }

def plot_learning_curves(estimator, X, y, cv=5, scoring='f1_macro'):
    """绘制学习曲线"""
    print(f"\n📈 绘制学习曲线")
    
    train_sizes = np.linspace(0.1, 1.0, 10)
    train_sizes_abs, train_scores, val_scores = learning_curve(
        estimator, X, y, cv=cv, scoring=scoring, 
        train_sizes=train_sizes, random_state=42
    )
    
    # 计算均值和标准差
    train_mean = np.mean(train_scores, axis=1)
    train_std = np.std(train_scores, axis=1)
    val_mean = np.mean(val_scores, axis=1)
    val_std = np.std(val_scores, axis=1)
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(train_sizes_abs, train_mean, 'o-', color='blue', label='训练集')
    plt.fill_between(train_sizes_abs, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')
    
    plt.plot(train_sizes_abs, val_mean, 'o-', color='red', label='验证集')
    plt.fill_between(train_sizes_abs, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')
    
    plt.xlabel('训练样本数')
    plt.ylabel(f'{scoring.upper()} 分数')
    plt.title('学习曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 保存图片
    plt.savefig('learning_curves.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 学习曲线已保存: learning_curves.png")
    
    # 分析结果
    final_gap = train_mean[-1] - val_mean[-1]
    print(f"最终性能差距: {final_gap:.4f}")
    
    return {
        'train_sizes': train_sizes_abs.tolist(),
        'train_scores': train_mean.tolist(),
        'val_scores': val_mean.tolist(),
        'final_gap': float(final_gap)
    }

def diagnose_data_leakage(X_train, X_test, threshold=0.95):
    """诊断数据泄露"""
    print(f"\n🔍 数据泄露诊断")
    print("=" * 30)
    
    # 检查重复样本
    train_df = pd.DataFrame(X_train)
    test_df = pd.DataFrame(X_test)
    
    # 计算相似度
    similarities = []
    for i, test_row in test_df.iterrows():
        max_sim = 0
        for j, train_row in train_df.iterrows():
            # 计算余弦相似度
            dot_product = np.dot(test_row, train_row)
            norm_product = np.linalg.norm(test_row) * np.linalg.norm(train_row)
            if norm_product > 0:
                similarity = dot_product / norm_product
                max_sim = max(max_sim, similarity)
        similarities.append(max_sim)
    
    high_sim_count = sum(1 for sim in similarities if sim > threshold)
    
    print(f"高相似度样本数 (>{threshold}): {high_sim_count}/{len(similarities)}")
    print(f"平均最大相似度: {np.mean(similarities):.4f}")
    print(f"相似度标准差: {np.std(similarities):.4f}")
    
    if high_sim_count > len(similarities) * 0.1:
        print("⚠️ 可能存在数据泄露")
        return True
    else:
        print("✅ 未发现明显数据泄露")
        return False

def suggest_anti_overfitting_config():
    """生成防过拟合配置建议"""
    print(f"\n💡 防过拟合配置建议")
    print("=" * 40)
    
    suggestions = {
        "XGBoost": {
            "n_estimators": "300-500 (减少树数量)",
            "max_depth": "3-5 (降低树深度)",
            "learning_rate": "0.05-0.1 (适中学习率)",
            "subsample": "0.6-0.8 (降低采样率)",
            "colsample_bytree": "0.6-0.8 (特征采样)",
            "reg_alpha": "1.0-5.0 (L1正则化)",
            "reg_lambda": "1.0-5.0 (L2正则化)",
            "gamma": "1.0-3.0 (最小分裂损失)",
            "early_stopping_rounds": "30-50"
        },
        "CatBoost": {
            "iterations": "500-1000 (减少迭代)",
            "depth": "3-5 (降低深度)",
            "learning_rate": "0.05-0.1 (适中学习率)",
            "l2_leaf_reg": "5.0-15.0 (L2正则化)",
            "random_strength": "1.0-3.0 (增加随机性)",
            "subsample": "0.6-0.8 (降低采样率)",
            "early_stopping_rounds": "30-50"
        },
        "数据预处理": {
            "SMOTE策略": "minority (只对少数类)",
            "PCA保留方差": "0.85-0.95 (适度降维)",
            "交叉验证折数": "5-10 (充分验证)"
        },
        "训练策略": {
            "验证集比例": "0.2-0.3 (充足验证)",
            "早停耐心": "20-50 轮",
            "性能监控": "训练/验证差距 < 0.1"
        }
    }
    
    for category, params in suggestions.items():
        print(f"\n📋 {category}:")
        for param, value in params.items():
            print(f"  {param}: {value}")
    
    return suggestions

def run_overfitting_diagnosis():
    """运行完整的过拟合诊断"""
    print("🔬 过拟合诊断工具")
    print("=" * 50)
    
    # 检查是否有训练结果
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("❌ 未找到results目录，请先运行训练")
        return
    
    # 查找分析文件
    analysis_file = os.path.join(results_dir, "class_1_analysis.json")
    if os.path.exists(analysis_file):
        import json
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis = json.load(f)
        
        # 分析训练测试差距
        if 'overall_performance' in analysis:
            perf = analysis['overall_performance']
            # 这里需要训练集性能数据，暂时模拟
            train_f1 = 0.996  # 从你的截图看到的训练性能
            test_f1 = perf.get('macro_f1', 0.0)
            
            gap_analysis = analyze_train_test_gap(train_f1, test_f1, "Macro-F1")
            
            # 保存诊断结果
            diagnosis = {
                'gap_analysis': gap_analysis,
                'timestamp': pd.Timestamp.now().isoformat(),
                'suggestions': suggest_anti_overfitting_config()
            }
            
            with open(os.path.join(results_dir, "overfitting_diagnosis.json"), 'w', encoding='utf-8') as f:
                json.dump(diagnosis, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 诊断结果已保存: {results_dir}/overfitting_diagnosis.json")
    
    # 生成配置建议
    suggest_anti_overfitting_config()
    
    print(f"\n🎯 下一步行动:")
    print(f"1. 使用防过拟合配置重新训练:")
    print(f"   python run.py --config_override config_anti_overfit.py")
    print(f"2. 监控训练过程中的验证性能")
    print(f"3. 使用更强的正则化和早停策略")
    print(f"4. 考虑增加更多真实数据")

if __name__ == "__main__":
    run_overfitting_diagnosis()
