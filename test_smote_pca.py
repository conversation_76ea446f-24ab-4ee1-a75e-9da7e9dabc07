#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_smote_pca.py
----------------
测试SMOTE-NC和PCA功能
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 创建不平衡的分类数据
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_classes=4,
        n_informative=15,
        n_redundant=5,
        n_clusters_per_class=1,
        weights=[0.6, 0.1, 0.2, 0.1],  # 不平衡分布
        random_state=42
    )
    
    # 添加一些分类特征
    categorical_features = []
    for i in range(3):  # 添加3个分类特征
        cat_feature = np.random.choice(['A', 'B', 'C', 'D'], size=len(X))
        categorical_features.append(cat_feature)
    
    # 合并数值和分类特征
    feature_names = [f'num_feature_{i}' for i in range(X.shape[1])]
    feature_names.extend([f'cat_feature_{i}' for i in range(len(categorical_features))])
    
    # 创建DataFrame
    X_df = pd.DataFrame(X, columns=[f'num_feature_{i}' for i in range(X.shape[1])])
    for i, cat_feat in enumerate(categorical_features):
        X_df[f'cat_feature_{i}'] = cat_feat
    
    # 添加一些缺失值
    missing_mask = np.random.random(X_df.shape) < 0.05  # 5%缺失率
    X_df = X_df.mask(missing_mask)
    
    print(f"✅ 测试数据创建完成:")
    print(f"   样本数: {len(X_df)}")
    print(f"   特征数: {X_df.shape[1]} (数值: {X.shape[1]}, 分类: {len(categorical_features)})")
    print(f"   类别分布: {dict(zip(*np.unique(y, return_counts=True)))}")
    print(f"   缺失值比例: {X_df.isnull().sum().sum() / X_df.size:.2%}")
    
    return X_df, y, feature_names

def test_data_preprocessor():
    """测试数据预处理器"""
    print("\n🧪 测试数据预处理器")
    print("=" * 40)
    
    try:
        from run import DataPreprocessor
        
        # 创建测试数据
        X, y, feature_names = create_test_data()
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        print(f"\n📊 数据分割:")
        print(f"   训练集: {X_train.shape}")
        print(f"   测试集: {X_test.shape}")
        
        # 测试不同配置
        configs = [
            ("SMOTE-NC + PCA", True, True, 0.95),
            ("只用SMOTE-NC", True, False, None),
            ("只用PCA", False, True, 0.90),
            ("无预处理", False, False, None)
        ]
        
        results = {}
        
        for name, enable_smote, enable_pca, pca_components in configs:
            print(f"\n🔬 测试配置: {name}")
            print("-" * 30)
            
            try:
                # 创建预处理器
                preprocessor = DataPreprocessor(
                    enable_smote=enable_smote,
                    enable_pca=enable_pca,
                    pca_components=pca_components or 0.95,
                    random_state=42
                )
                
                # 训练并转换
                X_train_processed, y_train_processed = preprocessor.fit_transform(X_train, y_train)
                X_test_processed = preprocessor.transform(X_test)
                
                # 记录结果
                results[name] = {
                    'train_shape': X_train_processed.shape,
                    'test_shape': X_test_processed.shape,
                    'train_samples_change': len(y_train_processed) - len(y_train),
                    'feature_reduction': X_train.shape[1] - X_train_processed.shape[1],
                    'success': True
                }
                
                print(f"✅ {name} 成功")
                print(f"   训练集形状变化: {X_train.shape} → {X_train_processed.shape}")
                print(f"   测试集形状变化: {X_test.shape} → {X_test_processed.shape}")
                print(f"   样本数变化: {len(y_train)} → {len(y_train_processed)}")
                
                if enable_smote:
                    original_dist = dict(zip(*np.unique(y_train, return_counts=True)))
                    new_dist = dict(zip(*np.unique(y_train_processed, return_counts=True)))
                    print(f"   类别分布变化: {original_dist} → {new_dist}")
                
            except Exception as e:
                print(f"❌ {name} 失败: {e}")
                results[name] = {'success': False, 'error': str(e)}
        
        # 总结结果
        print(f"\n📊 测试总结:")
        print("=" * 40)
        for name, result in results.items():
            if result['success']:
                print(f"✅ {name}")
                print(f"   训练集: {result['train_shape']}")
                print(f"   测试集: {result['test_shape']}")
                print(f"   样本增加: {result['train_samples_change']}")
                print(f"   特征减少: {result['feature_reduction']}")
            else:
                print(f"❌ {name}: {result['error']}")
            print()
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保安装了必要的依赖: pip install imbalanced-learn scikit-learn")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_detection():
    """测试特征检测功能"""
    print("\n🔍 测试特征检测")
    print("=" * 40)
    
    try:
        from run import DataPreprocessor
        
        # 创建测试数据
        X, y, feature_names = create_test_data()
        
        # 创建预处理器
        preprocessor = DataPreprocessor(enable_smote=False, enable_pca=False)
        
        # 检测分类特征
        categorical_features = preprocessor.detect_categorical_features(X)
        
        print(f"✅ 特征检测完成:")
        print(f"   总特征数: {X.shape[1]}")
        print(f"   检测到的分类特征数: {len(categorical_features)}")
        print(f"   分类特征索引: {categorical_features}")
        
        # 验证检测结果
        expected_categorical = [i for i, col in enumerate(X.columns) if 'cat_feature' in col]
        print(f"   期望的分类特征索引: {expected_categorical}")
        
        if set(categorical_features) == set(expected_categorical):
            print("✅ 特征检测准确")
        else:
            print("⚠️ 特征检测可能不完全准确")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征检测测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖库"""
    print("🔍 检查依赖库")
    print("=" * 40)
    
    dependencies = [
        ("imbalanced-learn", "imblearn"),
        ("scikit-learn", "sklearn"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("joblib", "joblib")
    ]
    
    missing = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - 请安装: pip install {name}")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    else:
        print("\n✅ 所有依赖都已安装")
        return True

def main():
    """主测试函数"""
    print("🧪 SMOTE-NC + PCA 功能测试")
    print("=" * 50)
    
    # 1. 检查依赖
    deps_ok = test_dependencies()
    if not deps_ok:
        return False
    
    # 2. 测试特征检测
    detection_ok = test_feature_detection()
    
    # 3. 测试数据预处理器
    preprocessor_ok = test_data_preprocessor()
    
    # 总结
    print(f"\n🎯 测试总结:")
    print("=" * 30)
    print(f"依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"特征检测: {'✅ 通过' if detection_ok else '❌ 失败'}")
    print(f"预处理器: {'✅ 通过' if preprocessor_ok else '❌ 失败'}")
    
    if deps_ok and detection_ok and preprocessor_ok:
        print(f"\n🎉 所有测试通过！SMOTE-NC + PCA 功能正常")
        return True
    else:
        print(f"\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
