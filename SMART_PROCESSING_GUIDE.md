# 智能数据处理使用指南

## 概述

现在 `run.py` 具备了智能数据处理功能：**自动检测是否存在已经过 SMOTE 处理的数据集，如果存在就直接使用，如果没有就重新处理**。

## 🧠 智能处理逻辑

### 工作流程
```
开始运行
    ↓
检查是否跳过预处理？
    ↓ 否
检查是否强制重新处理？
    ↓ 否
搜索现有预处理数据
    ↓
找到了？ → 是 → 验证数据完整性 → 使用现有数据
    ↓ 否
重新进行数据预处理
    ↓
保存预处理结果
    ↓
继续训练和测试
```

### 搜索位置
智能处理会在以下位置搜索预处理数据：
1. `config` 中指定的 `result_dir`
2. `results/` 目录
3. `output/` 目录  
4. `processed_data/` 目录
5. 当前目录 `.`

### 必需文件
- `processed_train_data.npz` - 预处理后的训练数据
- `processed_val_data.npz` - 预处理后的验证数据
- `preprocessor.pkl` - 预处理器（可选，用于测试数据处理）

## 🚀 使用方法

### 1. 基本使用（推荐）
```bash
# 首次运行 - 会进行数据预处理
python run.py --enable_smote --enable_pca

# 再次运行 - 会自动使用已处理的数据
python run.py --enable_smote --enable_pca
```

### 2. 强制重新处理
```bash
# 即使存在已处理数据，也强制重新处理
python run.py --enable_smote --enable_pca --force_reprocess
```

### 3. 跳过预处理
```bash
# 完全跳过预处理，使用原始数据
python run.py --skip_preprocessing
```

### 4. 不同配置
```bash
# 只使用 SMOTE-NC
python run.py --enable_smote --disable_pca

# 只使用 PCA
python run.py --disable_smote --enable_pca

# 不同的 PCA 配置
python run.py --enable_smote --enable_pca --pca_components 0.90
```

## 📊 运行示例

### 首次运行
```bash
$ python run.py --enable_smote --enable_pca

🎯 运行配置:
   SMOTE-NC: ✅ 启用
   PCA: ✅ 启用
   PCA组件: 0.95
   跳过预处理: 否
   强制重新处理: 否

🧠 智能数据处理
========================================
🔍 未找到现有的预处理数据
🔄 开始新的数据预处理...

🔧 开始数据预处理流程
==================================================
📂 加载原始数据...
   训练集: (1000, 20)
   验证集: (300, 20)
   特征数: 20
   原始类别分布: {0: 600, 1: 100, 2: 200, 3: 100}

🔧 数据预处理器初始化:
   SMOTE-NC: ✅ 启用
   PCA: ✅ 启用
   PCA组件: 0.95

🎯 应用SMOTE-NC过采样...
   原始类别分布: {0: 600, 1: 100, 2: 200, 3: 100}
   过采样后分布: {0: 600, 1: 600, 2: 600, 3: 600}
   样本数量变化: 1000 → 2400

🔍 应用PCA降维...
   原始特征数: 23
   PCA后特征数: 15
   解释方差比: 0.952
   降维比例: 34.8%

✅ 数据预处理完成
💾 预处理数据已保存:
   训练数据: results/processed_train_data.npz
   验证数据: results/processed_val_data.npz
   预处理器: results/preprocessor.pkl
```

### 第二次运行
```bash
$ python run.py --enable_smote --enable_pca

🎯 运行配置:
   SMOTE-NC: ✅ 启用
   PCA: ✅ 启用
   PCA组件: 0.95
   跳过预处理: 否
   强制重新处理: 否

🧠 智能数据处理
========================================
🔍 在 results 找到预处理数据
   ✅ 找到可用的预处理数据
✅ 找到现有预处理数据，直接使用
   位置: results
   预处理器: results/preprocessor.pkl
   训练数据: (2400, 15), 标签: (2400,)
   验证数据: (300, 15), 标签: (300,)
   类别分布: {0: 600, 1: 600, 2: 600, 3: 600}
✅ 数据验证通过，使用现有预处理数据
```

## 🔧 高级功能

### 配置匹配检查
智能处理会检查现有数据是否与当前配置匹配：
- SMOTE-NC 启用状态
- PCA 启用状态  
- PCA 组件数量
- 其他预处理参数

### 数据完整性验证
使用现有数据前会进行验证：
- 文件是否存在且可读
- 数据形状是否合理
- 标签分布是否正常

### 错误恢复
如果现有数据验证失败，会自动回退到重新处理：
```
❌ 现有数据验证失败: 文件损坏
   将重新进行数据预处理
🔄 开始新的数据预处理...
```

## 📁 文件结构

### 预处理后的文件结构
```
results/
├── processed_train_data.npz      # 预处理后的训练数据
├── processed_val_data.npz        # 预处理后的验证数据
├── preprocessor.pkl              # 预处理器
├── stacking_config.json          # 配置文件
├── stacking_model.pkl            # 堆叠模型
└── class_weights.pkl             # 类别权重
```

### 数据文件内容
```python
# processed_train_data.npz
{
    'X': numpy.ndarray,  # 预处理后的特征 (n_samples, n_features)
    'y': numpy.ndarray   # 标签 (n_samples,)
}

# processed_val_data.npz  
{
    'X': numpy.ndarray,  # 预处理后的特征 (n_samples, n_features)
    'y': numpy.ndarray   # 标签 (n_samples,)
}
```

## 🧪 测试功能

### 运行测试
```bash
# 测试智能处理功能
python test_smart_processing.py
```

### 测试内容
1. **首次运行测试** - 验证数据预处理和文件生成
2. **第二次运行测试** - 验证智能检测和数据复用
3. **强制重新处理测试** - 验证强制重新处理功能
4. **跳过预处理测试** - 验证原始数据使用

## 💡 最佳实践

### 1. 推荐工作流程
```bash
# 第一次运行项目
python run.py --enable_smote --enable_pca

# 后续运行（会自动使用已处理数据）
python run.py --enable_smote --enable_pca

# 如果更改了预处理配置，强制重新处理
python run.py --enable_smote --enable_pca --pca_components 0.90 --force_reprocess
```

### 2. 性能优化
- **首次运行**: 需要完整的数据预处理，时间较长
- **后续运行**: 直接使用已处理数据，启动更快
- **配置变更**: 使用 `--force_reprocess` 确保使用新配置

### 3. 存储管理
- 预处理数据会保存在 `results/` 目录
- 可以手动删除 `results/` 目录来强制重新处理
- 不同配置的数据可能需要分别存储

## ⚠️ 注意事项

### 1. 配置一致性
确保每次运行使用相同的预处理配置，否则可能需要重新处理：
```bash
# 这两个命令会使用不同的配置
python run.py --enable_smote --enable_pca --pca_components 0.95
python run.py --enable_smote --enable_pca --pca_components 0.90  # 不同配置
```

### 2. 数据更新
如果原始数据发生变化，需要强制重新处理：
```bash
python run.py --enable_smote --enable_pca --force_reprocess
```

### 3. 磁盘空间
预处理数据会占用额外的磁盘空间，特别是使用 SMOTE-NC 后样本数量增加。

## 🎯 总结

智能数据处理功能的优势：

✅ **提高效率**: 避免重复的数据预处理
✅ **自动化**: 无需手动管理预处理数据
✅ **灵活性**: 支持强制重新处理和跳过预处理
✅ **可靠性**: 包含数据验证和错误恢复
✅ **易用性**: 保持原有的使用方式

现在你可以更高效地使用 SMOTE-NC 和 PCA 功能，系统会智能地管理预处理数据！
