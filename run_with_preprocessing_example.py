#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_with_preprocessing_example.py
--------------------------------
使用SMOTE-NC和PCA的完整示例
"""

import os
import sys
import subprocess

def run_with_smote_pca():
    """使用SMOTE-NC和PCA运行完整流程"""
    print("🚀 运行带有SMOTE-NC和PCA的完整训练测试流程")
    print("=" * 60)
    
    # 基本配置
    cmd = [
        sys.executable, "run.py",
        "--enable_smote",      # 启用SMOTE-NC过采样
        "--enable_pca",        # 启用PCA降维
        "--pca_components", "0.95",  # 保留95%的方差
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    # 执行
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 执行成功!")
        print("\n📊 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("\n⚠️ 警告/错误:")
            print(result.stderr)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    return True

def run_without_preprocessing():
    """不使用预处理运行"""
    print("\n🔄 运行不带预处理的对比实验")
    print("=" * 60)
    
    cmd = [
        sys.executable, "run.py",
        "--disable_smote",     # 禁用SMOTE-NC
        "--disable_pca",       # 禁用PCA
        "--skip_preprocessing" # 跳过预处理
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 对比实验执行成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 对比实验失败: {e}")
        return False

def run_pca_only():
    """只使用PCA"""
    print("\n🔍 运行只使用PCA的实验")
    print("=" * 60)
    
    cmd = [
        sys.executable, "run.py",
        "--disable_smote",     # 禁用SMOTE-NC
        "--enable_pca",        # 启用PCA
        "--pca_components", "0.90"  # 保留90%的方差
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PCA实验执行成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PCA实验失败: {e}")
        return False

def run_smote_only():
    """只使用SMOTE-NC"""
    print("\n⚖️ 运行只使用SMOTE-NC的实验")
    print("=" * 60)
    
    cmd = [
        sys.executable, "run.py",
        "--enable_smote",      # 启用SMOTE-NC
        "--disable_pca"        # 禁用PCA
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ SMOTE-NC实验执行成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ SMOTE-NC实验失败: {e}")
        return False

def run_ablation_study():
    """运行消融研究"""
    print("🧪 开始消融研究")
    print("=" * 60)
    
    experiments = [
        ("基线（无预处理）", run_without_preprocessing),
        ("只使用SMOTE-NC", run_smote_only),
        ("只使用PCA", run_pca_only),
        ("SMOTE-NC + PCA", run_with_smote_pca)
    ]
    
    results = {}
    
    for name, func in experiments:
        print(f"\n🔬 实验: {name}")
        success = func()
        results[name] = success
        
        if success:
            print(f"✅ {name} 完成")
        else:
            print(f"❌ {name} 失败")
    
    # 总结
    print("\n📊 消融研究总结:")
    print("=" * 40)
    for name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {name}: {status}")
    
    return results

def main():
    """主函数"""
    print("🎯 SMOTE-NC + PCA 预处理示例")
    print("=" * 60)
    
    # 检查依赖
    try:
        import imblearn
        print("✅ imbalanced-learn 可用")
    except ImportError:
        print("❌ 请安装 imbalanced-learn: pip install imbalanced-learn")
        return
    
    try:
        import sklearn
        print("✅ scikit-learn 可用")
    except ImportError:
        print("❌ 请安装 scikit-learn: pip install scikit-learn")
        return
    
    # 选择运行模式
    import argparse
    parser = argparse.ArgumentParser(description="SMOTE-NC + PCA 预处理示例")
    parser.add_argument("--mode", choices=["full", "smote_only", "pca_only", "baseline", "ablation"], 
                       default="full", help="运行模式")
    args = parser.parse_args()
    
    if args.mode == "full":
        run_with_smote_pca()
    elif args.mode == "smote_only":
        run_smote_only()
    elif args.mode == "pca_only":
        run_pca_only()
    elif args.mode == "baseline":
        run_without_preprocessing()
    elif args.mode == "ablation":
        run_ablation_study()
    
    print("\n🎉 示例运行完成!")

if __name__ == "__main__":
    main()
