{"global": {"seed": 42, "class_weight": {"0": 0.8, "1": 24.0, "2": 3.5, "3": 2.5}, "verbose_data_info": true}, "paths": {"train_data": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\data_2\\train_data.csv", "val_data": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\data_2\\val_data.csv", "test_data": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\data_2\\test_data.csv", "xgb_model_path": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\xgb_model.json", "cat_model_path": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\cat_model.cbm", "attn_model_dir": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\attn_saved_model", "fusion_model": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\fusion_model.pkl", "meta_path": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\attention_meta.json", "result_dir": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2", "processed_train_data": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\processed_train_data.npz", "processed_val_data": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\processed_val_data.npz", "preprocessor": "E:\\python-program\\XGboost+CatBoost - 副本 (2)\\result_2\\preprocessor.pkl"}, "task": {"label_col": "RISK_LEVEL", "num_class": 4}, "xgb": {"objective": "multi:softprob", "num_class": 4, "eval_metric": "mlogloss", "random_state": 42, "n_estimators": 1200, "max_depth": 8, "learning_rate": 0.03, "subsample": 0.8, "colsample_bytree": 0.8, "gamma": 1.5, "reg_alpha": 1.5, "reg_lambda": 2.0, "tree_method": "gpu_hist", "max_delta_step": 1}, "cat": {"loss_function": "MultiClass", "eval_metric": "MultiClass", "iterations": 2500, "depth": 10, "learning_rate": 0.02, "l2_leaf_reg": 2.0, "random_seed": 42, "task_type": "GPU", "verbose": false, "auto_class_weights": "Balanced", "bootstrap_type": "Bayesian", "bagging_temperature": 1.0}, "attn": {"embed_dim": 256, "ff_dim": 512, "num_layers": 6, "num_heads": 12, "nb_features": 128, "dropout": 0.15, "use_cls_token": true, "kernel": "leaky_relu", "max_seq_len": 2048, "truncate": true, "chunk_size": 512, "batch_size": 24, "token_selection_strategy": "adaptive_class_aware", "token_selection": {"minority_detection_strategy": "dynamic", "threshold_ratio": 0.25, "min_samples_threshold": 1000, "adaptive_weights": true, "class_1_boost": 3.0}, "epochs": 120, "use_focal": true, "focal_alpha": [0.05, 0.45, 0.25, 0.25], "focal_gamma": 3.5, "class_1_boost": true, "class_1_boost_factor": 3.5, "class_3_boost": false, "use_class_aware_loss": true, "minority_boost": true, "adaptive_lr": true, "lr_scheduler": {"type": "cosine_with_warmup", "initial_lr": 0.0003, "warmup_epochs": 20, "patience": 12, "factor": 0.7, "min_lr": 5e-07, "cosine_restart_epochs": 40}}, "ensemble": {"optimize": true, "n_dirichlet": 3000, "local_refine": true, "use_stacking": true, "meta_learner": "lightgbm"}, "xgb_tune": {"n_estimators": [1000, 1200, 1500], "max_depth": [7, 8, 9], "learning_rate": [0.02, 0.03, 0.04], "subsample": [0.8, 0.85], "colsample_bytree": [0.8, 0.85]}, "cat_tune": {"depth": [8, 10, 12], "learning_rate": [0.015, 0.02, 0.025], "l2_leaf_reg": [1.5, 2.0, 2.5], "iterations": [2000, 2500, 3000]}, "test": {"max_tokens": 1024, "plots": {"roc": true, "confusion": true, "precision_recall": true, "class_distribution": true}}, "ultimate": {"mixed_precision": false, "advanced_augmentation": true, "progressive_training": true, "early_stopping": {"monitor": "val_weighted_accuracy", "patience": 15, "restore_best_weights": true, "min_delta": 0.001}, "class_balance_strategy": {"use_smote": true, "smote_k_neighbors": 5, "use_class_weights": true, "dynamic_weight_adjustment": true}}}