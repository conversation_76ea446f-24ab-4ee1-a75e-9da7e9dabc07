# -*- coding: utf-8 -*-
"""
test.py
-------
加载已训练模型，对测试集进行评估并绘图。
"""

from __future__ import annotations
import logging, os
from pathlib import Path
from typing import Dict, Any, Tuple

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import (
    confusion_matrix, classification_report,
    roc_curve, auc, precision_recall_curve,
    average_precision_score, f1_score
)

from config import config, sync_num_class
from tree_attention_pipeline import (
    load_csv,  load_all, StackingFusionWrapper
)

LOGGER = logging.getLogger(__name__)
if not LOGGER.handlers:
    logging.basicConfig(level=logging.INFO,
                        format="%(asctime)s - %(levelname)s - %(message)s")


# =====================================================================================
# 可视化
# =====================================================================================
def plot_confusion_and_save(y_true, y_pred, out_path):
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(dpi=300, figsize=(5, 4))
    plt.imshow(cm, cmap="Blues")
    plt.title("Confusion Matrix")
    plt.xlabel("Predicted");
    plt.ylabel("True")
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, cm[i, j], ha='center', va='center', color='red', fontsize=11)
    plt.colorbar(fraction=0.046, pad=0.04)
    plt.tight_layout();
    plt.savefig(out_path);
    plt.close()


def analyze_class_1_performance(y_true, y_pred, y_proba=None):
    """专门分析类别1的性能表现 - 增强版"""
    print("\n🎯 类别1性能详细分析")
    print("=" * 50)

    # 输入验证
    if len(y_true) != len(y_pred):
        raise ValueError(f"y_true和y_pred长度不匹配: {len(y_true)} vs {len(y_pred)}")

    if len(y_true) == 0:
        raise ValueError("输入数据为空")

    # 基础指标
    class_1_mask = (y_true == 1)
    class_1_pred_mask = (y_pred == 1)

    # 数据分布分析
    unique_true, counts_true = np.unique(y_true, return_counts=True)
    unique_pred, counts_pred = np.unique(y_pred, return_counts=True)

    print(f"📊 数据分布:")
    print(f"  真实标签分布: {dict(zip(unique_true, counts_true))}")
    print(f"  预测标签分布: {dict(zip(unique_pred, counts_pred))}")

    # 类别1样本统计
    total_samples = len(y_true)
    true_class_1_count = np.sum(class_1_mask)
    pred_class_1_count = np.sum(class_1_pred_mask)

    print(f"  总样本数: {total_samples}")
    print(f"  真实类别1样本数: {true_class_1_count} ({true_class_1_count/total_samples*100:.1f}%)")
    print(f"  预测类别1样本数: {pred_class_1_count} ({pred_class_1_count/total_samples*100:.1f}%)")

    # 计算各项指标
    true_positives = np.sum((y_true == 1) & (y_pred == 1))
    false_positives = np.sum((y_true != 1) & (y_pred == 1))
    false_negatives = np.sum((y_true == 1) & (y_pred != 1))
    true_negatives = np.sum((y_true != 1) & (y_pred != 1))

    # 验证混淆矩阵
    total_check = true_positives + false_positives + false_negatives + true_negatives
    if total_check != total_samples:
        print(f"⚠️ 混淆矩阵验证失败: {total_check} != {total_samples}")

    # 计算性能指标（增加数值稳定性）
    epsilon = 1e-10  # 避免除零
    precision = true_positives / (true_positives + false_positives + epsilon)
    recall = true_positives / (true_positives + false_negatives + epsilon)
    specificity = true_negatives / (true_negatives + false_positives + epsilon)
    f1 = 2 * (precision * recall) / (precision + recall + epsilon)

    # 额外指标
    accuracy = (true_positives + true_negatives) / total_samples
    balanced_accuracy = (recall + specificity) / 2

    print(f"\n📊 类别1混淆矩阵分析:")
    print(f"  真正例 (TP): {true_positives}")
    print(f"  假正例 (FP): {false_positives}")
    print(f"  假负例 (FN): {false_negatives}")
    print(f"  真负例 (TN): {true_negatives}")
    print(f"  总计验证: {true_positives + false_positives + false_negatives + true_negatives}")

    print(f"\n📈 类别1性能指标:")
    print(f"  精确率 (Precision): {precision:.4f}")
    print(f"  召回率 (Recall): {recall:.4f}")
    print(f"  特异性 (Specificity): {specificity:.4f}")
    print(f"  F1分数: {f1:.4f}")
    print(f"  准确率: {accuracy:.4f}")
    print(f"  平衡准确率: {balanced_accuracy:.4f}")

    # 性能等级评估
    def get_performance_level(score, thresholds=[0.8, 0.6, 0.4]):
        if score >= thresholds[0]:
            return "🌟 优秀"
        elif score >= thresholds[1]:
            return "✅ 良好"
        elif score >= thresholds[2]:
            return "🟡 一般"
        else:
            return "❌ 需要改进"

    print(f"\n📊 性能等级评估:")
    print(f"  精确率: {get_performance_level(precision)}")
    print(f"  召回率: {get_performance_level(recall)}")
    print(f"  F1分数: {get_performance_level(f1)}")

    # 类别1预测质量分析
    if true_class_1_count > 0:
        class_1_detection_rate = true_positives / true_class_1_count
        print(f"\n🎯 类别1检测分析:")
        print(f"  检测率: {class_1_detection_rate:.4f} ({true_positives}/{true_class_1_count})")
        print(f"  漏检数量: {false_negatives}")
        print(f"  误检数量: {false_positives}")

        if false_negatives > 0:
            print(f"  ⚠️ 有 {false_negatives} 个类别1样本被漏检")
        if false_positives > 0:
            print(f"  ⚠️ 有 {false_positives} 个非类别1样本被误检为类别1")

    # 如果有概率预测，分析概率分布
    probability_analysis = {}
    if y_proba is not None and y_proba.shape[1] > 1:
        class_1_proba = y_proba[:, 1]

        print(f"\n📊 类别1概率分布分析:")

        # 真实类别1样本的预测概率
        true_class_1_proba = class_1_proba[class_1_mask]
        if len(true_class_1_proba) > 0:
            true_stats = {
                'mean': float(np.mean(true_class_1_proba)),
                'median': float(np.median(true_class_1_proba)),
                'min': float(np.min(true_class_1_proba)),
                'max': float(np.max(true_class_1_proba)),
                'std': float(np.std(true_class_1_proba)),
                'count': int(len(true_class_1_proba))
            }

            print(f"  真实类别1样本的预测概率 (n={true_stats['count']}):")
            print(f"    平均值: {true_stats['mean']:.4f}")
            print(f"    中位数: {true_stats['median']:.4f}")
            print(f"    标准差: {true_stats['std']:.4f}")
            print(f"    范围: [{true_stats['min']:.4f}, {true_stats['max']:.4f}]")

            # 分析低概率的真实类别1样本
            low_confidence_threshold = 0.5
            low_confidence_count = np.sum(true_class_1_proba < low_confidence_threshold)
            if low_confidence_count > 0:
                print(f"    ⚠️ 低置信度样本: {low_confidence_count} 个 (概率 < {low_confidence_threshold})")

            probability_analysis['true_class_1'] = true_stats

        # 其他类别样本的类别1预测概率
        other_class_1_proba = class_1_proba[~class_1_mask]
        if len(other_class_1_proba) > 0:
            other_stats = {
                'mean': float(np.mean(other_class_1_proba)),
                'median': float(np.median(other_class_1_proba)),
                'min': float(np.min(other_class_1_proba)),
                'max': float(np.max(other_class_1_proba)),
                'std': float(np.std(other_class_1_proba)),
                'count': int(len(other_class_1_proba))
            }

            print(f"  其他类别样本的类别1预测概率 (n={other_stats['count']}):")
            print(f"    平均值: {other_stats['mean']:.4f}")
            print(f"    中位数: {other_stats['median']:.4f}")
            print(f"    标准差: {other_stats['std']:.4f}")
            print(f"    范围: [{other_stats['min']:.4f}, {other_stats['max']:.4f}]")

            # 分析高概率的非类别1样本
            high_confidence_threshold = 0.5
            high_confidence_count = np.sum(other_class_1_proba > high_confidence_threshold)
            if high_confidence_count > 0:
                print(f"    ⚠️ 高置信度误判: {high_confidence_count} 个 (概率 > {high_confidence_threshold})")

            probability_analysis['other_classes'] = other_stats

        # 概率分离度分析
        if len(true_class_1_proba) > 0 and len(other_class_1_proba) > 0:
            separation = true_stats['mean'] - other_stats['mean']
            print(f"\n📈 概率分离度分析:")
            print(f"  平均概率差: {separation:.4f}")
            if separation > 0.3:
                print(f"  ✅ 分离度良好")
            elif separation > 0.1:
                print(f"  🟡 分离度一般")
            else:
                print(f"  ❌ 分离度较差，模型区分能力有限")

            probability_analysis['separation'] = float(separation)

    # 智能改进建议
    suggestions = []
    print(f"\n💡 智能改进建议:")

    if recall < 0.3:
        suggestions.append("召回率严重不足")
        print("  🚨 召回率严重不足，紧急建议:")
        print("    - 大幅增加类别1的样本权重 (class_weight)")
        print("    - 使用SMOTE等过采样技术")
        print("    - 降低分类阈值 (如从0.5降到0.3)")
        print("    - 检查特征工程是否充分")
    elif recall < 0.6:
        suggestions.append("召回率偏低")
        print("  ⚠️ 召回率偏低，建议:")
        print("    - 适度增加类别1权重")
        print("    - 使用数据增强技术")
        print("    - 调整决策阈值")
        print("    - 考虑集成学习方法")

    if precision < 0.3:
        suggestions.append("精确率严重不足")
        print("  🚨 精确率严重不足，建议:")
        print("    - 检查数据质量和标签正确性")
        print("    - 改进特征选择")
        print("    - 使用更复杂的模型")
    elif precision < 0.6:
        suggestions.append("精确率偏低")
        print("  ⚠️ 精确率偏低，建议:")
        print("    - 适度降低类别1权重")
        print("    - 改进特征工程")
        print("    - 增加正则化")

    if f1 < 0.4:
        suggestions.append("F1分数过低")
        print("  🚨 F1分数过低，综合建议:")
        print("    - 重新审视问题定义和数据质量")
        print("    - 尝试不同的算法")
        print("    - 进行更深入的特征工程")

    if len(suggestions) == 0:
        print("  ✅ 性能表现良好，继续保持!")
        if f1 > 0.8:
            print("  🌟 模型表现优秀!")

    # 返回详细的分析结果
    result = {
        'precision': float(precision),
        'recall': float(recall),
        'specificity': float(specificity),
        'f1': float(f1),
        'accuracy': float(accuracy),
        'balanced_accuracy': float(balanced_accuracy),
        'true_positives': int(true_positives),
        'false_positives': int(false_positives),
        'false_negatives': int(false_negatives),
        'true_negatives': int(true_negatives),
        'total_samples': int(total_samples),
        'true_class_1_count': int(true_class_1_count),
        'pred_class_1_count': int(pred_class_1_count),
        'suggestions': suggestions
    }

    # 添加概率分析结果
    if probability_analysis:
        result['probability_analysis'] = probability_analysis

    return result


def plot_roc_and_save(y_true, probs, out_path):
    n_cls = probs.shape[1]
    plt.figure(dpi=300, figsize=(6, 5))
    for i in range(n_cls):
        fpr, tpr, _ = roc_curve(y_true == i, probs[:, i])
        r_auc = auc(fpr, tpr)
        plt.plot(fpr, tpr, label=f"Class {i} (AUC={r_auc:.2f})", linewidth=2)
    plt.plot([0, 1], [0, 1], '--', linewidth=2)
    plt.title("ROC Curve");
    plt.xlabel("FPR");
    plt.ylabel("TPR")
    plt.legend(fontsize=11);
    plt.tight_layout();
    plt.savefig(out_path);
    plt.close()


# =====================================================================================
# 主评估
# =====================================================================================
def test_main(cfg: Dict[str, Any] | None = None):
    if cfg is None:
        cfg = config
    paths = cfg["paths"]
    label_col = cfg["task"]["label_col"]

    try:
        # 载入模型
        xgb_model, cat_model, stacking_model, cls_w = load_all(paths)
        LOGGER.info("堆叠模型加载成功")

        # 载入测试数据
        X_ts, y_ts, _ = load_csv(paths["test_data"], label_col)

        # 检查是否需要应用预处理
        if "preprocessor" in paths and os.path.exists(paths["preprocessor"]):
            print("🔄 应用数据预处理到测试数据...")

            # 加载预处理器
            import joblib
            import pandas as pd

            preprocessor_data = joblib.load(paths["preprocessor"])

            # 重建预处理器（简化版）
            if preprocessor_data.get('enable_pca', False):
                from sklearn.preprocessing import StandardScaler
                from sklearn.decomposition import PCA

                scaler = preprocessor_data['scaler']
                pca = preprocessor_data['pca']
                feature_names = preprocessor_data['feature_names']
                categorical_features = preprocessor_data['categorical_features']

                # 转换为DataFrame
                X_ts_df = pd.DataFrame(X_ts, columns=feature_names)

                # 处理分类特征（简化处理）
                X_ts_processed = X_ts_df.copy()
                for i in categorical_features:
                    col = X_ts_df.columns[i]
                    X_ts_processed[col] = X_ts_processed[col].fillna('missing')
                    X_ts_processed[col] = pd.Categorical(X_ts_processed[col]).codes

                # 处理缺失值
                X_ts_processed = X_ts_processed.fillna(0)

                # 应用标准化和PCA
                X_ts_scaled = scaler.transform(X_ts_processed.values)
                X_ts = pca.transform(X_ts_scaled)

                print(f"✅ 测试数据预处理完成: {X_ts_processed.shape} → {X_ts.shape}")

        num_cls = int(cfg["task"]["num_class"] or len(np.unique(y_ts)))
        cfg = sync_num_class(cfg, num_cls)

        LOGGER.info(f"测试数据形状: X={X_ts.shape}, y={y_ts.shape}")

        # 构造堆叠融合 wrapper，增加错误处理
        try:
            wrapper = StackingFusionWrapper(
                xgb_model, cat_model, stacking_model, minority_boost=True
            )
            LOGGER.info("堆叠融合模型创建成功")
        except Exception as e:
            LOGGER.error(f"创建StackingFusionWrapper失败: {e}")
            raise

        # 预测，增加错误处理
        try:
            LOGGER.info("开始预测...")
            probs = wrapper.predict_proba(X_ts)
            preds = probs.argmax(1)
            LOGGER.info("预测完成")
        except Exception as e:
            LOGGER.error(f"预测失败: {e}")
            # 使用XGBoost作为fallback
            LOGGER.info("使用XGBoost fallback预测")
            probs = xgb_model.predict_proba(X_ts)
            preds = probs.argmax(1)

        # 🎯 专门的类别1性能分析
        print("\n" + "="*60)
        print("🎯 模型改进效果评估")
        print("="*60)

        class_1_metrics = analyze_class_1_performance(y_ts, preds, probs)

        # 整体性能评估
        overall_accuracy = float(np.mean(y_ts == preds))
        macro_f1 = float(f1_score(y_ts, preds, average='macro'))
        weighted_f1 = float(f1_score(y_ts, preds, average='weighted'))

        print(f"\n📊 整体性能:")
        print(f"  准确率: {overall_accuracy:.4f}")
        print(f"  宏平均F1: {macro_f1:.4f}")
        print(f"  加权平均F1: {weighted_f1:.4f}")
        print(f"  平衡准确率: {class_1_metrics['balanced_accuracy']:.4f}")

        # 多层次目标评估
        targets = {
            'accuracy': {'value': 0.85, 'weight': 0.3},
            'class_1_recall': {'value': 0.65, 'weight': 0.4},
            'class_1_f1': {'value': 0.60, 'weight': 0.3}
        }

        print(f"\n🎯 多层次目标达成评估:")

        # 基础目标
        accuracy_achieved = overall_accuracy >= targets['accuracy']['value']
        recall_achieved = class_1_metrics['recall'] >= targets['class_1_recall']['value']
        f1_achieved = class_1_metrics['f1'] >= targets['class_1_f1']['value']

        print(f"  📈 基础目标:")
        print(f"    准确率 (≥{targets['accuracy']['value']}): {'✅ 达成' if accuracy_achieved else '❌ 未达成'} ({overall_accuracy:.4f})")
        print(f"    类别1召回率 (≥{targets['class_1_recall']['value']}): {'✅ 达成' if recall_achieved else '❌ 未达成'} ({class_1_metrics['recall']:.4f})")
        print(f"    类别1 F1 (≥{targets['class_1_f1']['value']}): {'✅ 达成' if f1_achieved else '❌ 未达成'} ({class_1_metrics['f1']:.4f})")

        # 综合评分
        score_components = [
            overall_accuracy / targets['accuracy']['value'] * targets['accuracy']['weight'],
            class_1_metrics['recall'] / targets['class_1_recall']['value'] * targets['class_1_recall']['weight'],
            class_1_metrics['f1'] / targets['class_1_f1']['value'] * targets['class_1_f1']['weight']
        ]
        composite_score = sum(score_components)

        print(f"\n  🏆 综合评分: {composite_score:.3f}")
        if composite_score >= 1.0:
            print(f"    🌟 优秀 - 所有目标均达成")
        elif composite_score >= 0.8:
            print(f"    ✅ 良好 - 大部分目标达成")
        elif composite_score >= 0.6:
            print(f"    🟡 一般 - 部分目标达成")
        else:
            print(f"    ❌ 需要改进 - 多数目标未达成")

        # 输出文本报告
        rep = classification_report(y_ts, preds)
        out_dir = Path(paths["result_dir"])
        out_dir.mkdir(parents=True, exist_ok=True)
        (out_dir / "classification_report.txt").write_text(rep, encoding="utf-8")

        # 保存详细的分析结果
        class_1_analysis = {
            "overall_performance": {
                "accuracy": float(overall_accuracy),
                "macro_f1": float(macro_f1),
                "weighted_f1": float(weighted_f1),
                "balanced_accuracy": float(class_1_metrics['balanced_accuracy'])
            },
            "class_1_metrics": class_1_metrics,
            "target_achievement": {
                "targets": targets,
                "achievements": {
                    "accuracy": accuracy_achieved,
                    "class_1_recall": recall_achieved,
                    "class_1_f1": f1_achieved
                },
                "composite_score": float(composite_score)
            },
            "model_info": {
                "total_test_samples": int(len(y_ts)),
                "prediction_timestamp": pd.Timestamp.now().isoformat(),
                "model_type": "stacking_ensemble"
            }
        }

        import json

        # 自定义JSON编码器，处理NumPy数据类型
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.bool_):
                    return bool(obj)
                return super().default(obj)

        with open(out_dir / "class_1_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(class_1_analysis, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        LOGGER.info("classification_report 已写入 %s", out_dir)

        # 混淆矩阵
        if cfg["test"]["plots"].get("confusion", True):
            plot_confusion_and_save(y_ts, preds, out_dir / "confusion_matrix.png")
        # ROC
        if cfg["test"]["plots"].get("roc", True):
            plot_roc_and_save(y_ts, probs, out_dir / "roc_curve.png")

        # 保存概率
        df_prob = pd.DataFrame(probs, columns=[f"p_{i}" for i in range(num_cls)])
        df_prob["y_true"] = y_ts
        df_prob["y_pred"] = preds
        df_prob.to_csv(out_dir / "test_probs.csv", index=False)

        LOGGER.info("测试完成，结果保存至 %s", out_dir)
        return probs, preds, y_ts

    except Exception as e:
        LOGGER.error(f"测试过程出错: {e}")
        raise


if __name__ == "__main__":
    test_main()
