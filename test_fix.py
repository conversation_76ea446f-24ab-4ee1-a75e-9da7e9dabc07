#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_fix.py
----------
测试修复是否成功
"""

def test_train_import():
    """测试train.py导入"""
    try:
        print("测试 train.py 导入...")
        import train
        print("✅ train.py 导入成功")
        return True
    except Exception as e:
        print(f"❌ train.py 导入失败: {e}")
        return False

def test_test_import():
    """测试test.py导入"""
    try:
        print("测试 test.py 导入...")
        import test
        print("✅ test.py 导入成功")
        return True
    except Exception as e:
        print(f"❌ test.py 导入失败: {e}")
        return False

def test_run_import():
    """测试run.py导入"""
    try:
        print("测试 run.py 导入...")
        import run
        print("✅ run.py 导入成功")
        return True
    except Exception as e:
        print(f"❌ run.py 导入失败: {e}")
        return False

def main():
    print("🔧 测试修复结果")
    print("=" * 20)
    
    train_ok = test_train_import()
    test_ok = test_test_import()
    run_ok = test_run_import()
    
    print(f"\n结果:")
    print(f"train.py: {'✅' if train_ok else '❌'}")
    print(f"test.py: {'✅' if test_ok else '❌'}")
    print(f"run.py: {'✅' if run_ok else '❌'}")
    
    if train_ok and test_ok and run_ok:
        print(f"\n🎉 所有导入都成功！修复完成")
        print(f"\n现在可以运行:")
        print(f"python run.py --enable_smote --enable_pca")
    else:
        print(f"\n⚠️ 仍有导入问题")

if __name__ == "__main__":
    main()
