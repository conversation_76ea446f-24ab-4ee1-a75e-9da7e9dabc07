# -*- coding: utf-8 -*-
"""
tree_attention_pipeline.py
--------------------------
TensorFlow 版树 + Performer-LeafTransformer 流水。

包含：
1. 随机种子 & 工具函数
2. 数据加载
3. 类别权重计算
4. XGBoost / CatBoost 训练与叶索引提取
5. 叶序列拼接（含深度）
6. PerformerSelfAttention + LeafTransformer 模型定义
7. 融合权重自动搜索
8. 保存 / 加载 / 预测封装
"""

from __future__ import annotations
import os, json, logging, joblib
from pathlib import Path
from typing import Dict, Tuple, Optional, Sequence, Any

import numpy as np
import pandas as pd
import tensorflow as tf

from sklearn.metrics import f1_score, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight
from sklearn.model_selection import train_test_split

from xgboost import XGBClassifier
from catboost import CatBoostClassifier, Pool


# ---------- 自定义安全 rsqrt (正向剪 inv，上界；反向剪 grad) ----------
@tf.custom_gradient
def safe_rsqrt(var, eps=1e-6, inv_max=32., grad_clip=1e4):
    var = tf.clip_by_value(var, eps, 1e4)  # 方差下界
    inv = tf.math.rsqrt(var)
    inv = tf.clip_by_value(inv, 0., inv_max)  # 1/√var 上界设为32

    def grad(dinv):
        # d(inv)/d(var) = -0.5 * var^{-1.5}
        g = -0.5 * tf.pow(inv, 3)
        g = tf.clip_by_value(g, -grad_clip, grad_clip)
        return dinv * g, None, None, None

    return inv, grad


LOGGER = logging.getLogger(__name__)
if not LOGGER.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


class SafeLayerNorm(tf.keras.layers.Layer):
    def __init__(self, epsilon=1e-6, inv_max=32., grad_clip=1e4, **kw):
        super().__init__(**kw)
        self.eps, self.inv_max, self.grad_clip = float(epsilon), float(inv_max), float(grad_clip)

    def build(self, shape):
        self.gamma = self.add_weight("gamma", shape=shape[-1:], initializer="ones", trainable=True)
        self.beta = self.add_weight("beta", shape=shape[-1:], initializer="zeros", trainable=True)
        self._printed = self.add_weight(
            name="printed", shape=(), dtype=tf.bool,
            initializer="zeros", trainable=False)

    def call(self, x):
        x32 = tf.cast(x, tf.float32)
        mu = tf.reduce_mean(x32, -1, keepdims=True)
        var = tf.reduce_mean(tf.square(x32 - mu), -1, keepdims=True)

        # 使用更稳定的方差处理
        var = tf.maximum(var, self.eps)  # 确保方差不会太小
        inv = safe_rsqrt(var, self.eps, self.inv_max, self.grad_clip)
        y = (x32 - mu) * inv
        y = tf.cast(y, x.dtype)

        return self.gamma * y + self.beta


# =====================================================================================
# 随机种子
# =====================================================================================
def seed_all(seed: int):
    import random, os
    random.seed(seed)
    np.random.seed(seed)
    tf.random.set_seed(seed)

    # —— 显存按需分配 ——
    for g in tf.config.experimental.list_physical_devices('GPU'):
        tf.config.experimental.set_memory_growth(g, True)

    os.environ["PYTHONHASHSEED"] = str(seed)


# =====================================================================================
# 数据加载
# =====================================================================================
def load_csv(csv_path: str, label_col: str) -> Tuple[np.ndarray, np.ndarray, Sequence[str]]:
    df = pd.read_csv(csv_path)
    feat_cols = [c for c in df.columns if c != label_col]
    X = df[feat_cols].values.astype(np.float32)
    y = df[label_col].values.astype(int)
    return X, y, feat_cols


def maybe_infer_val_split(train_csv: str,
                          val_csv: Optional[str],
                          label_col: str,
                          val_size: float = 0.2,
                          seed: int = 42) -> Tuple[
    Tuple[np.ndarray, np.ndarray], Tuple[np.ndarray, np.ndarray], Sequence[str]]:
    """
    若提供 val_csv 则直接读；否则从 train_csv 中划分验证集。
    """
    if val_csv and Path(val_csv).is_file():
        X_tr, y_tr, feat_cols = load_csv(train_csv, label_col)
        X_va, y_va, _ = load_csv(val_csv, label_col)
        return (X_tr, y_tr), (X_va, y_va), feat_cols
    # split
    df = pd.read_csv(train_csv)
    feat_cols = [c for c in df.columns if c != label_col]
    X = df[feat_cols].values.astype(np.float32)
    y = df[label_col].values.astype(int)
    X_tr, X_va, y_tr, y_va = train_test_split(
        X, y, test_size=val_size, random_state=seed, stratify=y)
    return (X_tr, y_tr), (X_va, y_va), feat_cols


# =====================================================================================
# 类别权重
# =====================================================================================
def infer_class_weight(y, mode="auto"):
    if isinstance(mode, dict):
        return mode
    if mode is None:
        return {int(c): 1.0 for c in np.unique(y)}
    if mode == "auto":
        classes = np.unique(y)
        cw = compute_class_weight('balanced', classes=classes, y=y)
        return dict(zip(classes, cw))
    raise ValueError(mode)


# =====================================================================================
# XGB / CatBoost 训练
# =====================================================================================
# =====================================================================================
#  XGB / CatBoost 训练（修复重复关键字 & 过滤无效参数）
# =====================================================================================

# =====================================================================================
#  XGB / CatBoost 训练（修复重复关键字 & 过滤无效/冲突参数）
# =====================================================================================

def _filter_xgb_params(p: Dict) -> Dict:
    """
    过滤掉 sklearn.XGBClassifier 不识别或与显式传参重复的键。
    - device: 不被 sklearn.XGBClassifier 接受；用 tree_method 控制 GPU。
    """
    drop_keys = {"device"}  # 根据需要可继续添加
    return {k: v for k, v in p.items() if k not in drop_keys and v is not None}


def train_xgb(X, y, sample_weight, base_params: Dict, param_grid: Optional[Dict] = None):
    """
    简单网格搜索 + 全量重训。
    修复：正确处理类别权重字典
    """
    from sklearn.metrics import f1_score
    from xgboost import XGBClassifier

    rng = np.random.default_rng(42)
    idx = np.arange(len(X))
    rng.shuffle(idx)
    hold = int(len(X) * 0.15)
    tr, va = idx[hold:], idx[:hold]
    Xtr, Xva, ytr, yva = X[tr], X[va], y[tr], y[va]

    # 修复：正确处理sample_weight
    sw_tr = None
    if sample_weight is not None:
        if isinstance(sample_weight, dict):
            # 如果是字典，转换为数组
            sw_array = np.ones(len(y))
            for class_id, weight in sample_weight.items():
                sw_array[y == class_id] = weight
            sw_tr = sw_array[tr]
        else:
            # 如果已经是数组
            sw_tr = sample_weight[tr]

    # 参数网格
    pg = param_grid or {}
    ne_list = pg.get("n_estimators", [base_params.get("n_estimators", 100)])
    md_list = pg.get("max_depth", [base_params.get("max_depth", 6)])
    lr_list = pg.get("learning_rate", [base_params.get("learning_rate", 0.1)])

    base_f = _filter_xgb_params(base_params)

    best_f1 = -1.0
    best_m = None
    for ne in ne_list:
        for md in md_list:
            for lr in lr_list:
                p = dict(base_f)
                p.update(dict(n_estimators=ne, max_depth=md, learning_rate=lr))
                m = XGBClassifier(**p, verbosity=0)
                m.fit(Xtr, ytr, sample_weight=sw_tr)
                pred = m.predict(Xva)
                f1 = f1_score(yva, pred, average='macro')
                if f1 > best_f1:
                    best_f1, best_m = f1, m

    LOGGER.info("XGB 内部搜索最佳 macro-F1=%.4f", best_f1)

    # 全数据重训 - 修复sample_weight处理
    final_sw = None
    if sample_weight is not None:
        if isinstance(sample_weight, dict):
            final_sw = np.ones(len(y))
            for class_id, weight in sample_weight.items():
                final_sw[y == class_id] = weight
        else:
            final_sw = sample_weight

    best_m.fit(X, y, sample_weight=final_sw)
    return best_m


def _filter_cat_params(p: Dict) -> Dict:
    """
    过滤冲突键：
    - loss_function / eval_metric 我们在构造器显式传，避免重复
    - verbose 统一在构造器控制
    - subsample: 与默认 bootstrap_type='Bayesian' 冲突；如需 subsample 改 Bernoulli，可自行调整
    """
    drop_keys = {"loss_function", "eval_metric", "verbose", "subsample"}
    return {k: v for k, v in p.items() if k not in drop_keys and v is not None}


def train_cat(X, y, sample_weight, base_params: Dict, param_grid: Optional[Dict] = None):
    """
    CatBoost 训练 - 修复类别权重处理
    """
    from sklearn.metrics import f1_score
    from catboost import CatBoostClassifier, Pool

    rng = np.random.default_rng(42)
    idx = np.arange(len(X))
    rng.shuffle(idx)
    hold = int(len(X) * 0.15)
    tr, va = idx[hold:], idx[:hold]
    Xtr, Xva, ytr, yva = X[tr], X[va], y[tr], y[va]

    # 修复：正确处理sample_weight
    sw_tr = None
    if sample_weight is not None:
        if isinstance(sample_weight, dict):
            sw_array = np.ones(len(y))
            for class_id, weight in sample_weight.items():
                sw_array[y == class_id] = weight
            sw_tr = sw_array[tr]
        else:
            sw_tr = sample_weight[tr]

    # 参数处理...
    pg = param_grid or {}
    dp_list = pg.get("depth", [base_params.get("depth", 6)])
    lr_list = pg.get("learning_rate", [base_params.get("learning_rate", 0.1)])
    l2_list = pg.get("l2_leaf_reg", [base_params.get("l2_leaf_reg", 3.0)])

    base_f = _filter_cat_params(base_params)

    best_f1 = -1.0
    best_m = None
    for dp in dp_list:
        for lr in lr_list:
            for l2 in l2_list:
                p = dict(base_f)
                p.update(dict(depth=dp, learning_rate=lr, l2_leaf_reg=l2))
                m = CatBoostClassifier(**p, verbose=False)

                # 创建Pool对象
                train_pool = Pool(Xtr, ytr, weight=sw_tr)
                m.fit(train_pool)

                pred = m.predict(Xva)
                f1 = f1_score(yva, pred, average='macro')
                if f1 > best_f1:
                    best_f1, best_m = f1, m

    LOGGER.info("Cat 内部搜索最佳 macro-F1=%.4f", best_f1)

    # 全数据重训
    final_sw = None
    if sample_weight is not None:
        if isinstance(sample_weight, dict):
            final_sw = np.ones(len(y))
            for class_id, weight in sample_weight.items():
                final_sw[y == class_id] = weight
        else:
            final_sw = sample_weight

    final_pool = Pool(X, y, weight=final_sw)
    best_m.fit(final_pool)
    return best_m


# =====================================================================================
# 叶索引 + 深度
# =====================================================================================
def cat_leaf_depth(model: CatBoostClassifier, X: np.ndarray):
    pool = Pool(X)
    leaf = model.calc_leaf_indexes(pool)  # (B,T)
    depth = np.rint(np.log2(leaf + 1)).astype(int)
    return leaf, depth


def xgb_leaf_depth(model: XGBClassifier, X: np.ndarray):
    leaf = model.apply(X)  # shape (B,T[,1])
    if leaf.ndim == 3:
        leaf = leaf[:, :, 0]
    depth = np.rint(np.log2(leaf + 1)).astype(int)
    return leaf, depth


# =====================================================================================
# 删除了所有token构建函数，现在使用堆叠方法
# =====================================================================================


# =====================================================================================
# 删除了build_tokens_with_selection函数
# =====================================================================================


# =====================================================================================
# 删除了所有token选择相关的辅助函数
# =====================================================================================

# =====================================================================================
# 删除了剩余的token选择代码
# =====================================================================================


def _calculate_token_depths(leaves):
    """计算token深度信息"""
    # 基于叶子节点ID估算深度
    depths = np.ceil(np.log2(leaves + 2)).astype(np.int32)

    # 限制深度范围
    depths = np.clip(depths, 1, 20)

    return depths


def evaluate_token_quality(leaves, y, importance):
    """评估token质量"""
    n_tokens = leaves.shape[1]

    print(f"\n🔍 Token质量评估:")
    print(f"  总token数: {n_tokens}")

    # 1. 重要性分布
    print(f"  重要性统计: 均值={np.mean(importance):.3f}, 标准差={np.std(importance):.3f}")

    # 2. 多样性评估
    unique_ratios = []
    for i in range(n_tokens):
        unique_ratio = len(np.unique(leaves[:, i])) / len(leaves)
        unique_ratios.append(unique_ratio)

    avg_diversity = np.mean(unique_ratios)
    print(f"  平均多样性: {avg_diversity:.3f} (1.0为完全多样)")

    # 3. 类别区分能力 (已删除，现在使用堆叠方法)
    avg_discrimination = None
    if y is not None:
        print(f"  类别区分能力: 已迁移到堆叠方法中")

    # 4. 给出建议
    if avg_diversity < 0.3:
        print("  ⚠️ Token多样性较低，建议增加diversity策略权重")
    elif avg_diversity > 0.8:
        print("  ✅ Token多样性良好")

    return {
        'diversity': avg_diversity,
        'discrimination': avg_discrimination if y is not None else None,
        'importance_stats': {'mean': np.mean(importance), 'std': np.std(importance)}
    }


from sklearn.model_selection import cross_val_predict
try:
    import lightgbm as lgb
except ImportError:
    print("警告: LightGBM未安装，将使用RandomForest作为元学习器")
    lgb = None

class StackingEnsemble:
    """
    堆叠集成学习器 - 使用交叉验证训练元学习器
    """
    def __init__(self, base_models=None, meta_model=None, cv_folds=5, random_state=42):
        """
        Args:
            base_models: 基学习器列表 [(name, model), ...]
            meta_model: 元学习器，默认使用LightGBM
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.base_models = base_models or []
        self.meta_model = meta_model or lgb.LGBMClassifier(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=random_state,
            verbose=-1
        )
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.fitted_base_models = {}

    def add_base_model(self, name, model):
        """添加基学习器"""
        self.base_models.append((name, model))

    def fit(self, X, y, sample_weight=None):
        """
        训练堆叠模型
        """
        print(f"🚀 开始训练堆叠集成模型 (基学习器: {len(self.base_models)}个)")

        # 1. 使用交叉验证生成基学习器的预测作为元特征
        meta_features = np.zeros((len(X), len(self.base_models)))

        for i, (name, model) in enumerate(self.base_models):
            print(f"  训练基学习器 {i+1}/{len(self.base_models)}: {name}")

            # 交叉验证预测
            cv_preds = cross_val_predict(
                model, X, y,
                cv=self.cv_folds,
                method='predict_proba',
                n_jobs=-1
            )

            # 使用预测概率的最大值作为元特征
            meta_features[:, i] = np.max(cv_preds, axis=1)

            # 在全数据上训练基学习器
            if sample_weight is not None:
                if hasattr(model, 'fit') and 'sample_weight' in model.fit.__code__.co_varnames:
                    model.fit(X, y, sample_weight=sample_weight)
                else:
                    model.fit(X, y)
            else:
                model.fit(X, y)

            self.fitted_base_models[name] = model

        # 2. 训练元学习器
        print("  训练元学习器...")
        if sample_weight is not None and hasattr(self.meta_model, 'fit') and 'sample_weight' in self.meta_model.fit.__code__.co_varnames:
            self.meta_model.fit(meta_features, y, sample_weight=sample_weight)
        else:
            self.meta_model.fit(meta_features, y)

        print("✅ 堆叠集成模型训练完成")
        return self

    def predict_proba(self, X):
        """预测概率"""
        # 1. 获取基学习器预测
        base_predictions = np.zeros((len(X), len(self.base_models)))

        for i, (name, _) in enumerate(self.base_models):
            model = self.fitted_base_models[name]
            proba = model.predict_proba(X)
            base_predictions[:, i] = np.max(proba, axis=1)

        # 2. 元学习器预测
        return self.meta_model.predict_proba(base_predictions)

    def predict(self, X):
        """预测类别"""
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)


# =====================================================================================
# 高级堆叠融合方法
# =====================================================================================

class AdvancedStackingEnsemble:
    """
    高级堆叠集成学习器 - 支持多层堆叠和特征工程
    """
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.level1_models = {}
        self.level2_model = None
        self.feature_names = []

    def add_level1_model(self, name, model):
        """添加第一层模型"""
        self.level1_models[name] = model

    def fit(self, X, y, sample_weight=None):
        """训练多层堆叠模型"""
        print(f"🚀 开始训练高级堆叠模型")

        # 1. 训练第一层模型并生成元特征
        meta_features = self._generate_meta_features(X, y, sample_weight)

        # 2. 特征工程 - 添加统计特征
        enhanced_features = self._feature_engineering(meta_features, X)

        # 3. 训练第二层模型 (元学习器)
        if lgb is not None:
            self.level2_model = lgb.LGBMClassifier(
                n_estimators=200,
                learning_rate=0.05,
                max_depth=8,
                num_leaves=64,
                feature_fraction=0.8,
                bagging_fraction=0.8,
                bagging_freq=5,
                random_state=self.random_state,
                verbose=-1
            )
        else:
            from sklearn.ensemble import RandomForestClassifier
            self.level2_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=8,
                random_state=self.random_state,
                n_jobs=-1
            )

        print("  训练第二层元学习器...")
        if sample_weight is not None:
            self.level2_model.fit(enhanced_features, y, sample_weight=sample_weight)
        else:
            self.level2_model.fit(enhanced_features, y)

        print("✅ 高级堆叠模型训练完成")
        return self

    def _generate_meta_features(self, X, y, sample_weight):
        """生成元特征"""
        from sklearn.model_selection import StratifiedKFold

        n_models = len(self.level1_models)
        n_classes = len(np.unique(y))
        meta_features = np.zeros((len(X), n_models * n_classes))

        # 使用分层K折交叉验证
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)

        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
            print(f"  处理第 {fold+1}/5 折...")

            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]

            sw_train = sample_weight[train_idx] if sample_weight is not None else None

            for i, (name, model) in enumerate(self.level1_models.items()):
                # 训练模型
                if sw_train is not None and hasattr(model, 'fit') and 'sample_weight' in model.fit.__code__.co_varnames:
                    model.fit(X_train, y_train, sample_weight=sw_train)
                else:
                    model.fit(X_train, y_train)

                # 预测验证集
                val_proba = model.predict_proba(X_val)
                meta_features[val_idx, i*n_classes:(i+1)*n_classes] = val_proba

        # 在全数据上重新训练所有模型
        for name, model in self.level1_models.items():
            if sample_weight is not None and hasattr(model, 'fit') and 'sample_weight' in model.fit.__code__.co_varnames:
                model.fit(X, y, sample_weight=sample_weight)
            else:
                model.fit(X, y)

        return meta_features

    def _feature_engineering(self, meta_features, original_features):
        """特征工程 - 添加统计特征"""
        n_models = len(self.level1_models)
        n_classes = meta_features.shape[1] // n_models

        # 基础元特征
        features = [meta_features]

        # 添加统计特征
        for i in range(n_models):
            model_proba = meta_features[:, i*n_classes:(i+1)*n_classes]

            # 最大概率
            max_proba = np.max(model_proba, axis=1, keepdims=True)
            features.append(max_proba)

            # 概率熵
            entropy = -np.sum(model_proba * np.log(model_proba + 1e-8), axis=1, keepdims=True)
            features.append(entropy)

            # 概率方差
            variance = np.var(model_proba, axis=1, keepdims=True)
            features.append(variance)

        # 模型间一致性特征
        predictions = np.argmax(meta_features.reshape(-1, n_models, n_classes), axis=2)
        consistency = np.mean(predictions == predictions[:, [0]], axis=1, keepdims=True)
        features.append(consistency)

        return np.concatenate(features, axis=1)

    def predict_proba(self, X):
        """预测概率"""
        # 1. 获取第一层预测
        n_classes = len(np.unique([0, 1, 2, 3]))  # 假设4个类别
        meta_features = np.zeros((len(X), len(self.level1_models) * n_classes))

        for i, (name, model) in enumerate(self.level1_models.items()):
            proba = model.predict_proba(X)
            meta_features[:, i*n_classes:(i+1)*n_classes] = proba

        # 2. 特征工程
        enhanced_features = self._feature_engineering(meta_features, X)

        # 3. 第二层预测
        return self.level2_model.predict_proba(enhanced_features)

    def predict(self, X):
        """预测类别"""
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)


# =====================================================================================
# 简化的堆叠融合包装器
# =====================================================================================

class SimpleStackingWrapper:
    """
    简化的堆叠融合包装器 - 专注于XGBoost和CatBoost的堆叠
    """
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.xgb_model = None
        self.cat_model = None
        self.meta_model = None
        self.is_fitted = False

    def fit(self, X, y, xgb_model, cat_model, sample_weight=None):
        """
        训练堆叠模型

        Args:
            X: 特征数据
            y: 标签
            xgb_model: 已训练的XGBoost模型
            cat_model: 已训练的CatBoost模型
            sample_weight: 样本权重
        """
        print("🚀 开始训练简化堆叠模型")

        self.xgb_model = xgb_model
        self.cat_model = cat_model

        # 获取基模型预测作为元特征
        xgb_proba = xgb_model.predict_proba(X)
        cat_proba = cat_model.predict_proba(X)

        # 构造元特征：[xgb_proba, cat_proba, 统计特征]
        meta_features = self._create_meta_features(xgb_proba, cat_proba)

        # 训练元学习器 - 优先使用LightGBM，否则使用RandomForest
        if lgb is not None:
            self.meta_model = lgb.LGBMClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                num_leaves=31,
                feature_fraction=0.9,
                bagging_fraction=0.8,
                bagging_freq=5,
                random_state=self.random_state,
                verbose=-1
            )
        else:
            from sklearn.ensemble import RandomForestClassifier
            self.meta_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=self.random_state,
                n_jobs=-1
            )

        if sample_weight is not None:
            self.meta_model.fit(meta_features, y, sample_weight=sample_weight)
        else:
            self.meta_model.fit(meta_features, y)

        self.is_fitted = True
        print("✅ 简化堆叠模型训练完成")

    def _create_meta_features(self, xgb_proba, cat_proba):
        """创建元特征"""
        features = []

        # 1. 原始概率
        features.append(xgb_proba)
        features.append(cat_proba)

        # 2. 统计特征
        # 最大概率
        xgb_max = np.max(xgb_proba, axis=1, keepdims=True)
        cat_max = np.max(cat_proba, axis=1, keepdims=True)
        features.extend([xgb_max, cat_max])

        # 概率差异
        prob_diff = np.abs(xgb_proba - cat_proba)
        features.append(prob_diff)

        # 预测一致性
        xgb_pred = np.argmax(xgb_proba, axis=1)
        cat_pred = np.argmax(cat_proba, axis=1)
        consistency = (xgb_pred == cat_pred).astype(float).reshape(-1, 1)
        features.append(consistency)

        # 平均概率
        avg_proba = (xgb_proba + cat_proba) / 2
        features.append(avg_proba)

        return np.concatenate(features, axis=1)

    def predict_proba(self, X):
        """预测概率"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 获取基模型预测
        xgb_proba = self.xgb_model.predict_proba(X)
        cat_proba = self.cat_model.predict_proba(X)

        # 创建元特征
        meta_features = self._create_meta_features(xgb_proba, cat_proba)

        # 元学习器预测
        return self.meta_model.predict_proba(meta_features)

    def predict(self, X):
        """预测类别"""
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)


# =====================================================================================
# 删除了所有注意力相关的代码，现在专注于堆叠方法
# =====================================================================================


# =====================================================================================
# 删除了EnhancedLeafTransformer类，现在使用堆叠方法
# =====================================================================================

# =====================================================================================
# 删除了所有注意力模型相关代码
# =====================================================================================


# =====================================================================================
# 删除了TreeAttentionPipeline类，现在专注于堆叠方法
# =====================================================================================

    @tf.function
    def train_step(self, data):
        # 延迟初始化：在第一次训练步骤时初始化
        if not self._grad_accum_initialized and self.built:
            self._initialize_grad_accum()

        # 如果还没初始化，使用标准训练步骤
        if not self._grad_accum_initialized:
            return super().train_step(data)

        return self._train_step_with_accum(data)

    @tf.function
    def _train_step_with_accum(self, data):
        if isinstance(data, (tuple, list)) and len(data) == 3:
            x, y, sw = data
        else:
            x, y = data
            sw = None

        with tf.GradientTape() as tape:
            y_pred = self(x, training=True)
            loss = self.compiled_loss(y, y_pred, sample_weight=sw)

            # 动态学习率调整（基于损失）
            if self.adaptive_lr:
                current_lr = tf.keras.backend.get_value(self.optimizer.lr)
                # 如果损失过大，降低学习率
                if loss > 2.0:
                    new_lr = current_lr * 0.9
                    tf.keras.backend.set_value(self.optimizer.lr, new_lr)
                # 如果损失很小且稳定，可以尝试提高学习率
                elif loss < 0.5:
                    new_lr = min(current_lr * 1.01, 1e-3)
                    tf.keras.backend.set_value(self.optimizer.lr, new_lr)

            if hasattr(self.optimizer, 'get_scaled_loss'):
                scaled_loss = self.optimizer.get_scaled_loss(loss)
            else:
                scaled_loss = loss

        # 计算梯度
        grads = tape.gradient(scaled_loss, self.trainable_variables)

        # 处理混合精度梯度
        if hasattr(self.optimizer, 'get_unscaled_gradients'):
            grads = self.optimizer.get_unscaled_gradients(grads)

        # 检查梯度是否有效
        grads = [tf.where(tf.math.is_finite(g), g, tf.zeros_like(g)) if g is not None else None for g in grads]

        # 梯度累积
        for i, grad in enumerate(grads):
            if grad is not None and i < len(self._grad_accum):
                self._grad_accum[i].assign_add(grad)

        self._step.assign_add(1)

        # 应用梯度的条件
        should_apply = tf.equal(self._step % self.accum_steps, 0)

        def apply_gradients():
            # 平均累积的梯度
            averaged_grads = []
            for acc in self._grad_accum:
                avg_grad = acc / tf.cast(self.accum_steps, acc.dtype)
                # 再次检查数值稳定性
                avg_grad = tf.where(tf.math.is_finite(avg_grad), avg_grad, tf.zeros_like(avg_grad))
                averaged_grads.append(avg_grad)

            # 应用平均后的梯度
            self.optimizer.apply_gradients(zip(averaged_grads, self.trainable_variables))

            # 重置累积梯度
            for acc in self._grad_accum:
                acc.assign(tf.zeros_like(acc))

            self._step.assign(0)  # 重置步数计数器
            return loss

        def skip_gradients():
            return loss

        final_loss = tf.cond(should_apply, apply_gradients, skip_gradients)

        # 更新指标
        self.compiled_metrics.update_state(y, y_pred, sample_weight=sw)

        return {m.name: m.result() for m in self.metrics}

    @tf.function
    def test_step(self, data):
        # 解包输入数据
        if isinstance(data, (tuple, list)):
            if len(data) == 3:
                x, y, sw = data
            else:
                x, y, sw = data[0], data[1], None
        else:
            x, y, sw = data, None, None

        # 进行前向推理
        y_pred = self(x, training=False)  # 确保模型处于推理模式

        # 计算损失
        loss = self.compiled_loss(y, y_pred, sample_weight=sw, regularization_losses=self.losses)

        # 更新指标（如准确度、AUC等）
        self.compiled_metrics.update_state(y, y_pred, sample_weight=sw)

        # 返回损失和指标结果
        results = {m.name: m.result() for m in self.metrics}

        # 记录损失，或者可以选择将损失返回
        results["loss"] = loss  # 将损失值加入到返回结果中
        return results


# =====================================================================================
# 堆叠融合方法 - 替代权重搜索
# =====================================================================================
def train_stacking_ensemble(X, y, xgb_model, cat_model, sample_weight=None, cv_folds=5, random_state=42):
    """
    训练堆叠集成模型 - 替代原来的权重搜索方法

    Args:
        X: 特征数据
        y: 标签
        xgb_model: 已训练的XGBoost模型
        cat_model: 已训练的CatBoost模型
        sample_weight: 样本权重
        cv_folds: 交叉验证折数
        random_state: 随机种子

    Returns:
        trained_stacking_model: 训练好的堆叠模型
    """
    print("🚀 开始训练堆叠集成模型")

    # 创建堆叠模型
    stacking_model = SimpleStackingWrapper(random_state=random_state)

    # 训练堆叠模型
    stacking_model.fit(X, y, xgb_model, cat_model, sample_weight=sample_weight)

    # 评估堆叠模型性能
    train_proba = stacking_model.predict_proba(X)
    train_pred = np.argmax(train_proba, axis=1)
    train_f1 = f1_score(y, train_pred, average='macro')

    print(f"✅ 堆叠模型训练完成，训练集macro-F1: {train_f1:.4f}")

    return stacking_model


# =====================================================================================
# 保存 / 加载
# =====================================================================================
def save_all(xgb_model, cat_model, stacking_model, cls_w, cfg_paths: Dict):
    """
    保存所有模型 - 堆叠版本
    """
    os.makedirs(cfg_paths["result_dir"], exist_ok=True)

    # 保存树模型
    xgb_model.save_model(cfg_paths["xgb_model_path"])
    cat_model.save_model(cfg_paths["cat_model_path"])

    # 保存堆叠模型
    stacking_model_path = os.path.join(cfg_paths["result_dir"], "stacking_model.pkl")
    joblib.dump(stacking_model, stacking_model_path)

    # 保存类别权重
    cls_w_path = os.path.join(cfg_paths["result_dir"], "class_weights.pkl")
    joblib.dump(cls_w, cls_w_path)

    LOGGER.info("堆叠模型已保存到 %s", cfg_paths["result_dir"])


def load_all(cfg_paths: Dict):
    """
    加载所有模型 - 堆叠版本
    """
    try:
        # 加载XGBoost模型
        print("正在加载XGBoost模型...")
        xgb_model = XGBClassifier()
        xgb_model.load_model(cfg_paths["xgb_model_path"])
        print("XGBoost模型加载成功")

        # 加载CatBoost模型
        print("正在加载CatBoost模型...")
        cat_model = CatBoostClassifier()
        cat_model.load_model(cfg_paths["cat_model_path"])
        print("CatBoost模型加载成功")

        # 加载堆叠模型
        print("正在加载堆叠模型...")
        stacking_model_path = os.path.join(cfg_paths["result_dir"], "stacking_model.pkl")
        stacking_model = joblib.load(stacking_model_path)
        print("堆叠模型加载成功")

        # 加载类别权重
        print("正在加载类别权重...")
        cls_w_path = os.path.join(cfg_paths["result_dir"], "class_weights.pkl")
        cls_w = joblib.load(cls_w_path)
        print("类别权重加载成功")

        return xgb_model, cat_model, stacking_model, cls_w

    except Exception as e:
        print(f"模型加载失败: {e}")
        raise


# =====================================================================================
# 堆叠融合预测 Wrapper
# =====================================================================================
class StackingFusionWrapper:
    """
    堆叠融合预测包装器 - 替代原来的注意力融合方法
    """
    def __init__(self, xgb_model, cat_model, stacking_model, minority_boost=True):
        self.xgb = xgb_model
        self.cat = cat_model
        self.stacking_model = stacking_model
        self.minority_boost = minority_boost

    def predict_proba(self, X):
        """
        使用堆叠方法预测概率
        """
        try:
            # 直接使用堆叠模型预测
            probs = self.stacking_model.predict_proba(X)

            # 少数类增强（如果启用）
            if self.minority_boost:
                # 对类别1和类别2进行轻微增强
                probs[:, 1] *= 1.1  # 类别1增强10%
                probs[:, 2] *= 1.05  # 类别2增强5%

                # 重新归一化
                probs = probs / np.sum(probs, axis=1, keepdims=True)

            return probs

        except Exception as e:
            print(f"堆叠模型预测失败: {e}")
            # 回退到XGBoost预测
            try:
                return self.xgb.predict_proba(X)
            except Exception as e2:
                print(f"XGBoost预测也失败: {e2}")
                # 最后的回退：返回均匀分布
                n_classes = 4
                n_samples = len(X)
                return np.ones((n_samples, n_classes)) / n_classes

    def predict(self, X):
        """预测类别"""
        probs = self.predict_proba(X)

        # 使用动态阈值
        predictions = []
        for prob_row in probs:
            # 如果类别1或2的概率超过较低阈值，优先选择
            if prob_row[1] > 0.25:  # 类别1阈值
                pred = 1
            elif prob_row[2] > 0.28:  # 类别2阈值
                pred = 2
            else:
                pred = np.argmax(prob_row)
            predictions.append(pred)

        return np.array(predictions)


# =====================================================================================
# 删除了build_tokens_ultimate函数 - 现在使用堆叠方法
# =====================================================================================

# =====================================================================================
# 删除了数据增强相关代码
# =====================================================================================


# =====================================================================================
# 删除了另一个重复的build_tokens_ultimate函数
# =====================================================================================

# =====================================================================================
# 删除了数据增强相关代码
# =====================================================================================


# =====================================================================================
# 自定义损失函数
# =====================================================================================
class ClassAwareFocalLoss(tf.keras.losses.Loss):
    def __init__(self, alpha=None, gamma=2.0, class_weights=None, name="class_aware_focal_loss"):
        super().__init__(name=name)
        self.alpha = alpha
        self.gamma = gamma
        self.class_weights = class_weights

    def call(self, y_true, y_pred):
        # 转换为one-hot
        y_true = tf.cast(y_true, tf.int32)
        y_true_onehot = tf.one_hot(y_true, depth=tf.shape(y_pred)[-1])

        # 计算focal loss
        ce_loss = tf.keras.losses.categorical_crossentropy(y_true_onehot, y_pred)
        p_t = tf.reduce_sum(y_true_onehot * y_pred, axis=-1)

        # Alpha权重
        if self.alpha is not None:
            alpha_t = tf.reduce_sum(y_true_onehot * self.alpha, axis=-1)
            ce_loss = alpha_t * ce_loss

        # Focal权重
        focal_weight = tf.pow(1 - p_t, self.gamma)
        focal_loss = focal_weight * ce_loss

        # 类别权重
        if self.class_weights is not None:
            class_weight = tf.reduce_sum(y_true_onehot * self.class_weights, axis=-1)
            focal_loss = class_weight * focal_loss

        return tf.reduce_mean(focal_loss)


# =====================================================================================
# 动态学习率调度器
# =====================================================================================
class SmartLearningRateScheduler(tf.keras.callbacks.Callback):
    """智能学习率调度器 - 3个epoch没有进步就调整"""

    def __init__(self, monitor='val_accuracy', patience=3, factor=0.5,
                 min_lr=1e-7, verbose=1, mode='max'):
        super().__init__()
        self.monitor = monitor
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.verbose = verbose
        self.mode = mode

        # 内部状态
        self.wait = 0
        self.best_score = float('-inf') if mode == 'max' else float('inf')
        self.lr_reductions = 0

    def on_epoch_end(self, epoch, logs=None):
        current_score = logs.get(self.monitor)
        if current_score is None:
            return

        # 检查是否有改善
        improved = False
        if self.mode == 'max':
            improved = current_score > self.best_score
        else:
            improved = current_score < self.best_score

        if improved:
            self.best_score = current_score
            self.wait = 0
            if self.verbose:
                print(f"\n📈 Epoch {epoch + 1}: {self.monitor} 改善到 {current_score:.4f}")
        else:
            self.wait += 1
            if self.verbose:
                print(
                    f"\n⏳ Epoch {epoch + 1}: {self.monitor} 无改善 ({current_score:.4f}), 等待 {self.wait}/{self.patience}")

        # 3个epoch没有改善，调整学习率
        if self.wait >= self.patience:
            old_lr = tf.keras.backend.get_value(self.model.optimizer.lr)
            new_lr = max(old_lr * self.factor, self.min_lr)

            if new_lr != old_lr:
                tf.keras.backend.set_value(self.model.optimizer.lr, new_lr)
                self.lr_reductions += 1

                if self.verbose:
                    print(f"\n🔧 学习率调整 #{self.lr_reductions}: {old_lr:.2e} → {new_lr:.2e}")
                    print(f"   原因: {self.monitor} 连续 {self.patience} 个epoch无改善")

                # 重置等待计数
                self.wait = 0

                # 如果学习率已经很小，给出警告
                if new_lr <= self.min_lr:
                    print(f"⚠️  学习率已达到最小值 {self.min_lr:.2e}")
            else:
                if self.verbose:
                    print(f"⚠️  学习率已达到最小值，无法进一步降低")


class DynamicLearningRateScheduler(tf.keras.callbacks.Callback):
    """动态学习率调度器 - 基于训练状态智能调整"""

    def __init__(self, initial_lr=2e-4, patience=3, factor=0.5,
                 min_lr=1e-7, warmup_epochs=5, verbose=1):
        super().__init__()
        self.initial_lr = initial_lr
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.warmup_epochs = warmup_epochs
        self.verbose = verbose

        # 状态跟踪
        self.wait = 0
        self.best_val_acc = 0
        self.epoch_count = 0
        self.lr_reductions = 0

    def on_epoch_begin(self, epoch, logs=None):
        self.epoch_count = epoch

        # Warmup阶段
        if epoch < self.warmup_epochs:
            warmup_lr = self.initial_lr * (epoch + 1) / self.warmup_epochs
            tf.keras.backend.set_value(self.model.optimizer.lr, warmup_lr)
            if self.verbose and epoch == 0:
                print(f"🔥 开始Warmup: 学习率从 {warmup_lr:.2e} 开始")

    def on_epoch_end(self, epoch, logs=None):
        val_acc = logs.get('val_accuracy', 0)
        current_lr = tf.keras.backend.get_value(self.model.optimizer.lr)

        # Warmup阶段结束后开始正常调度
        if epoch >= self.warmup_epochs:
            if val_acc > self.best_val_acc:
                self.best_val_acc = val_acc
                self.wait = 0
                if self.verbose:
                    print(f"📈 验证准确率提升到 {val_acc:.4f}")
            else:
                self.wait += 1
                if self.verbose:
                    print(f"⏳ 验证准确率无改善 {self.wait}/{self.patience} epochs")

            # 3个epoch没有改善，调整学习率
            if self.wait >= self.patience:
                new_lr = max(current_lr * self.factor, self.min_lr)
                if new_lr != current_lr:
                    tf.keras.backend.set_value(self.model.optimizer.lr, new_lr)
                    self.lr_reductions += 1
                    self.wait = 0

                    print(f"🔧 学习率调整 #{self.lr_reductions}: {current_lr:.2e} → {new_lr:.2e}")

            # 显示当前状态
            print(f"📊 Epoch {epoch + 1}: LR={current_lr:.2e}, 最佳验证准确率={self.best_val_acc:.4f}")


class AdaptiveLearningRateCallback(tf.keras.callbacks.Callback):
    """自适应学习率回调 - 基于损失和准确率动态调整"""

    def __init__(self, initial_lr=2e-4, patience=3, min_lr=1e-7):
        super().__init__()
        self.initial_lr = initial_lr
        self.patience = patience
        self.min_lr = min_lr

        # 跟踪历史
        self.val_acc_history = []
        self.val_loss_history = []
        self.lr_history = []

        # 状态
        self.best_val_acc = 0
        self.epochs_without_improvement = 0

    def on_epoch_end(self, epoch, logs=None):
        val_acc = logs.get('val_accuracy', 0)
        val_loss = logs.get('val_loss', float('inf'))
        current_lr = tf.keras.backend.get_value(self.model.optimizer.lr)  # 修复：获取当前学习率

        # 记录历史
        self.val_acc_history.append(val_acc)
        self.val_loss_history.append(val_loss)
        self.lr_history.append(current_lr)

        # 检查是否有改善
        if val_acc > self.best_val_acc:  # 修复：添加缺失的条件判断
            self.best_val_acc = val_acc
            self.epochs_without_improvement = 0
            print(f"✅ Epoch {epoch + 1}: 新的最佳验证准确率 {val_acc:.4f}")
        else:
            self.epochs_without_improvement += 1

        # 3个epoch没有改善，动态调整学习率
        if self.epochs_without_improvement >= self.patience:
            # 分析最近的趋势
            if len(self.val_acc_history) >= 5:
                recent_acc = self.val_acc_history[-5:]
                recent_loss = self.val_loss_history[-5:]

                # 计算趋势
                acc_trend = recent_acc[-1] - recent_acc[0]
                loss_trend = recent_loss[-1] - recent_loss[0]

                # 决定调整策略
                if loss_trend > 0:
                    # 损失增加，降低学习率
                    new_lr = max(current_lr * 0.5, self.min_lr)
                    print(f"🔧 降低学习率: {current_lr:.2e} → {new_lr:.2e}")
                elif acc_trend < 0:
                    # 准确率下降，降低学习率
                    new_lr = max(current_lr * 0.5, self.min_lr)
                    print(f"🔧 降低学习率: {current_lr:.2e} → {new_lr:.2e}")
                else:
                    # 没有明显趋势，保持学习率不变
                    new_lr = current_lr
                    print(f"📊 保持学习率: {current_lr:.2e}")

                # 更新学习率
                tf.keras.backend.set_value(self.model.optimizer.lr, new_lr)
                self.epochs_without_improvement = 0  # 重置计数器
            else:
                # 不够多的历史数据，保持学习率不变
                print(f"📊 数据不足，保持学习率: {current_lr:.2e}")


# =====================================================================================
# 删除了_identify_minority_classes函数
# =====================================================================================


# =====================================================================================
# 删除了所有数据增强相关函数
# =====================================================================================


# =====================================================================================
# 删除了最后一个build_tokens_ultimate函数
# =====================================================================================


# =====================================================================================
# 删除了_calculate_class_imbalance_severity函数
# =====================================================================================


# =====================================================================================
# 删除了_select_class_aware_tokens_dynamic函数
# =====================================================================================


# =====================================================================================
# 删除了类别3相关的损失函数
# =====================================================================================


class UltimateLoss(tf.keras.losses.Loss):
    """终极损失函数 - 整合所有优化"""

    def __init__(self, num_classes=4, alpha=None, gamma=2.0, class_weights=None,
                 label_smoothing=0.1, class_3_boost=2.0, name="ultimate_loss"):
        super().__init__(name=name)
        self.num_classes = num_classes
        self.alpha = alpha or [0.1, 0.25, 0.2, 0.45]  # 类别3最高权重
        self.gamma = gamma
        self.class_weights = class_weights
        self.label_smoothing = label_smoothing
        self.class_3_boost = class_3_boost

    def call(self, y_true, y_pred):
        y_true = tf.cast(y_true, tf.int32)

        # 1. 标签平滑交叉熵
        y_true_onehot = tf.one_hot(y_true, depth=self.num_classes)
        smooth_loss = tf.keras.losses.categorical_crossentropy(
            y_true_onehot, y_pred, label_smoothing=self.label_smoothing
        )

        # 2. Focal Loss
        p_t = tf.reduce_sum(y_true_onehot * y_pred, axis=-1)
        alpha_t = tf.reduce_sum(y_true_onehot * self.alpha, axis=-1)
        focal_weight = alpha_t * tf.pow(1 - p_t, self.gamma)
        focal_loss = focal_weight * smooth_loss

        # 3. 类别权重
        if self.class_weights is not None:
            class_weight = tf.reduce_sum(y_true_onehot * self.class_weights, axis=-1)
            focal_loss = class_weight * focal_loss

        # 4. 类别3特殊增强
        class_3_mask = tf.equal(y_true, 3)
        class_3_penalty = tf.where(class_3_mask, focal_loss * self.class_3_boost, focal_loss)

        # 5. 预测错误的类别3额外惩罚
        pred_class = tf.argmax(y_pred, axis=-1)
        wrong_class_3 = tf.logical_and(class_3_mask, tf.not_equal(pred_class, 3))
        extra_penalty = tf.where(wrong_class_3, 1.0, 0.0)

        return tf.reduce_mean(class_3_penalty + extra_penalty)


class AdvancedTrainingStrategy:
    """高级训练策略类 - 不分阶段，全程智能学习率调度"""

    def __init__(self, model, cfg):
        self.model = model
        self.cfg = cfg
        self.attn_cfg = cfg["attn"]

    def progressive_training(self, train_data, val_data):
        """统一训练 - 全程3轮没有进步就调整学习率"""
        print("🎯 开始统一训练模式")

        leaf_tr, depth_tr, y_tr = train_data
        leaf_va, depth_va, y_va = val_data

        # 统一训练配置
        self.model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        # 全程使用智能学习率调度
        callbacks = [
            SmartLearningRateScheduler(
                monitor='val_accuracy',
                patience=3,  # 3个epoch没有进步就调整
                factor=0.5,
                min_lr=1e-7,
                verbose=1
            ),
            tf.keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=20,  # 给学习率调整充分机会
                restore_best_weights=True,
                verbose=1
            )
        ]

        # 一次性训练完成
        history = self.model.fit(
            {"leaf_ids": leaf_tr, "depths": depth_tr}, y_tr,
            validation_data=({"leaf_ids": leaf_va, "depths": depth_va}, y_va),
            epochs=100,  # 总训练轮次
            batch_size=self.attn_cfg["batch_size"],
            callbacks=callbacks,
            verbose=1
        )

        print("✅ 统一训练完成")
        return self.model


class UltimateTrainingMonitor(tf.keras.callbacks.Callback):
    """终极训练监控"""

    def __init__(self, stage_idx):
        super().__init__()
        self.stage_idx = stage_idx
        self.best_val_acc = 0
        self.patience_count = 0

    def on_epoch_end(self, epoch, logs=None):
        val_acc = logs.get('val_accuracy', 0)
        val_loss = logs.get('val_loss', float('inf'))

        if val_acc > self.best_val_acc:
            self.best_val_acc = val_acc
            self.patience_count = 0
            print(f"🎯 阶段{self.stage_idx + 1} 新纪录: 准确率 {val_acc:.4f}")
        else:
            self.patience_count += 1

            # 每10轮输出详细统计
            print(f"📊 阶段{self.stage_idx + 1} 第{epoch + 1}轮统计:")
            print(f"   当前准确率: {val_acc:.4f}, 最佳: {self.best_val_acc:.4f}")
            print(f"   当前损失: {val_loss:.4f}")
            print(f"   耐心计数: {self.patience_count}")


class StrictClass3RecallCallback(tf.keras.callbacks.Callback):
    """严格的类别3召回率监控"""

    def __init__(self):
        super().__init__()
        self.class_3_recall_history = []

    def on_epoch_end(self, epoch, logs=None):
        # 这里需要在验证时计算类别3的召回率
        # 简化版本，实际使用时需要传入验证数据
        if hasattr(self, 'val_data'):
            # 计算类别3召回率的逻辑
            pass

        # 如果类别3召回率连续多轮为0，触发特殊处理
        if len(self.class_3_recall_history) >= 5:
            recent_recalls = self.class_3_recall_history[-5:]
            if all(r < 0.01 for r in recent_recalls):
                print("🚨 类别3召回率持续过低，触发紧急调整")
                # 这里可以动态调整学习率或权重


# =====================================================================================
# 🎯 专门的类别1优化函数
# =====================================================================================

def create_class_1_optimized_loss(num_classes=4, class_1_weight=5.0):
    """创建专门优化类别1的损失函数"""

    def class_1_focal_loss(y_true, y_pred):
        # 转换标签格式
        y_true = tf.cast(y_true, tf.int32)
        y_true_onehot = tf.one_hot(y_true, depth=num_classes)

        # 计算交叉熵
        epsilon = tf.keras.backend.epsilon()
        y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)
        ce_loss = -tf.reduce_sum(y_true_onehot * tf.math.log(y_pred), axis=-1)

        # 🎯 特别关注类别1
        class_1_mask = tf.cast(tf.equal(y_true, 1), tf.float32)

        # 对类别1应用更强的权重
        class_weights = tf.where(class_1_mask > 0, class_1_weight, 1.0)

        # Focal loss权重
        p_t = tf.reduce_sum(y_true_onehot * y_pred, axis=-1)
        focal_weight = tf.pow(1.0 - p_t, 2.0)  # gamma=2.0

        # 组合损失
        loss = class_weights * focal_weight * ce_loss

        return tf.reduce_mean(loss)

    return class_1_focal_loss


def create_class_1_recall_callback(val_data, target_recall=0.7):
    """创建专门监控类别1召回率的回调"""

    class Class1RecallCallback(tf.keras.callbacks.Callback):
        def __init__(self, val_data, target_recall):
            super().__init__()
            self.val_data = val_data
            self.target_recall = target_recall
            self.class_1_recalls = []
            self.best_recall = 0.0

        def on_epoch_end(self, epoch, logs=None):
            # 收集验证数据
            y_true_list = []
            y_pred_list = []

            for batch in self.val_data:
                if len(batch) == 3:
                    x_batch, y_batch, _ = batch
                else:
                    x_batch, y_batch = batch

                y_pred_batch = self.model(x_batch, training=False)
                y_true_batch = tf.argmax(y_batch, axis=1)
                y_pred_batch = tf.argmax(y_pred_batch, axis=1)

                y_true_list.append(y_true_batch.numpy())
                y_pred_list.append(y_pred_batch.numpy())

            y_true = np.concatenate(y_true_list)
            y_pred = np.concatenate(y_pred_list)

            # 计算类别1的召回率
            class_1_mask = (y_true == 1)
            class_1_true_positives = np.sum((y_true == 1) & (y_pred == 1))
            class_1_actual_positives = np.sum(class_1_mask)

            if class_1_actual_positives > 0:
                class_1_recall = class_1_true_positives / class_1_actual_positives
            else:
                class_1_recall = 0.0

            self.class_1_recalls.append(class_1_recall)

            # 更新最佳召回率
            if class_1_recall > self.best_recall:
                self.best_recall = class_1_recall
                print(f"🎯 类别1召回率新纪录: {class_1_recall:.3f}")

            # 状态评估
            if class_1_recall >= self.target_recall:
                status = "🌟 优秀"
            elif class_1_recall >= 0.5:
                status = "✅ 良好"
            elif class_1_recall >= 0.3:
                status = "🟡 一般"
            else:
                status = "❌ 需要改进"

            print(f"📊 Epoch {epoch+1} - 类别1召回率: {class_1_recall:.3f} {status}")

            # 趋势分析
            if len(self.class_1_recalls) >= 3:
                recent_trend = np.mean(self.class_1_recalls[-3:]) - np.mean(self.class_1_recalls[-6:-3]) if len(self.class_1_recalls) >= 6 else 0
                if recent_trend > 0.05:
                    print(f"📈 类别1召回率呈上升趋势 (+{recent_trend:.3f})")
                elif recent_trend < -0.05:
                    print(f"📉 类别1召回率呈下降趋势 ({recent_trend:.3f})")

    return Class1RecallCallback(val_data, target_recall)


def optimize_for_class_1_recall(model, train_data, val_data, config):
    """专门优化类别1召回率的训练策略"""

    print("🎯 启动类别1召回率优化训练")
    print("=" * 50)

    # 1. 使用专门的损失函数
    class_1_loss = create_class_1_optimized_loss(
        num_classes=config["task"]["num_class"],
        class_1_weight=8.0  # 类别1权重
    )

    # 2. 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss=class_1_loss,
        metrics=['accuracy']
    )

    # 3. 设置回调
    callbacks = [
        create_class_1_recall_callback(val_data, target_recall=0.7),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.7,
            patience=8,
            min_lr=1e-7,
            verbose=1
        )
    ]

    # 4. 训练
    history = model.fit(
        train_data,
        validation_data=val_data,
        epochs=config["attn"]["epochs"],
        callbacks=callbacks,
        verbose=1
    )

    print("🏆 类别1优化训练完成")
    return model, history


class DynamicWeightAdjustmentCallback(tf.keras.callbacks.Callback):
    """动态权重调整回调"""

    def __init__(self):
        super().__init__()
        self.adjustment_count = 0

    def on_epoch_end(self, epoch, logs=None):
        val_acc = logs.get('val_accuracy', 0)

        # 每20轮检查一次是否需要调整
        if epoch > 0 and epoch % 20 == 0:
            if val_acc < 0.7:  # 如果准确率还不够高
                print(f"🔧 第{epoch}轮动态调整: 当前准确率 {val_acc:.4f}")
                self.adjustment_count += 1


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model


def train_single_attention_model(attn_model, leaf_tr, depth_tr, y_tr,
                                 leaf_va, depth_va, y_va, cfg):
    """训练单个注意力模型 - 使用智能学习率调度"""
    print("🚀 开始训练注意力模型")

    attn_cfg = cfg["attn"]

    # 编译模型
    attn_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-4),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    # 准备训练数据
    train_data = {
        "leaf_ids": leaf_tr,
        "depths": depth_tr
    }

    val_data = {
        "leaf_ids": leaf_va,
        "depths": depth_va
    }

    # 回调函数 - 使用智能学习率调度器
    callbacks = [
        SmartLearningRateScheduler(
            monitor='val_accuracy',
            patience=3,  # 3个epoch没有进步就调整
            factor=0.5,
            min_lr=1e-7,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=15,  # 更长的耐心，让学习率调整有机会发挥作用
            restore_best_weights=True,
            verbose=1
        )
    ]

    # 训练模型
    history = attn_model.fit(
        train_data, y_tr,
        validation_data=(val_data, y_va),
        epochs=cfg.get("epochs", 100),
        batch_size=attn_cfg["batch_size"],
        callbacks=callbacks,
        verbose=1
    )

    print("✅ 注意力模型训练完成")
    return attn_model








