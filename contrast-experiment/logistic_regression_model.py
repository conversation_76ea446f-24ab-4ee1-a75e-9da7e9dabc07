import os
import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    accuracy_score, recall_score, f1_score,
    classification_report, confusion_matrix, roc_curve, auc
)
import joblib
import matplotlib.pyplot as plt

# 输出目录
output_dir = '../result_1/logistic_regression_model'
os.makedirs(output_dir, exist_ok=True)

# 加载数据
train_data = pd.read_csv('../data_1/train_data.csv')
val_data   = pd.read_csv('../data_1/val_data.csv')
test_data  = pd.read_csv('../data_1/test_data.csv')

# 特征与目标
def prepare(data):
    X = data.drop(columns=['RISK_LEVEL'])
    y = data['RISK_LEVEL'].values
    return X, y

X_train, y_train = prepare(train_data)
X_val,   y_val   = prepare(val_data)
X_test,  y_test  = prepare(test_data)

# 训练模型
logreg_model = LogisticRegression(max_iter=200, random_state=42)
logreg_model.fit(X_train, y_train)
# 保存模型
joblib.dump(logreg_model, os.path.join(output_dir, 'logistic_regression_model.pkl'))

# 验证集评估
val_pred = logreg_model.predict(X_val)
print(f"验证集准确率: {accuracy_score(y_val, val_pred):.4f}")
print(f"验证集分类报告:\n{classification_report(y_val, val_pred)}")

# 测试集预测
y_pred = logreg_model.predict(X_test)
probs  = logreg_model.predict_proba(X_test)

# 计算指标
acc = accuracy_score(y_test, y_pred)
rec = recall_score(y_test, y_pred, average='weighted')
f1  = f1_score(y_test, y_pred, average='weighted')

# 保存分类报告
report_dict = classification_report(y_test, y_pred, output_dict=True)
df_report   = pd.DataFrame(report_dict).T
# 重命名列为中文
df_report = df_report.rename(columns={
    'precision': '精确率', 'recall': '召回率',
    'f1-score': 'F1分数', 'support': '样本数'
})
df_report.to_csv(os.path.join(output_dir, 'logistic_regression_report.csv'), encoding='utf-8-sig')

# 保存指标到文本
with open(os.path.join(output_dir, 'logistic_regression_metrics.txt'), 'w', encoding='utf-8') as f:
    f.write(f"准确率(Accuracy)：{acc:.4f}\n")
    f.write(f"召回率(Recall)：  {rec:.4f}\n")
    f.write(f"F1 分数(F1)：    {f1:.4f}\n\n")
    f.write("—— 详细分类报告 ——\n")
    f.write(classification_report(y_test, y_pred))

import numpy as np
import itertools
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix

# …（前面训练与预测代码不变）…

# --------- 新的混淆矩阵绘制 ---------
cm = confusion_matrix(y_test, y_pred)
classes = np.unique(y_test)
fig, ax = plt.subplots(figsize=(6,6))
im = ax.imshow(cm, interpolation='nearest', cmap='Blues')
ax.set_title('Confusion Matrix')
plt.colorbar(im, ax=ax)

tick_marks = np.arange(len(classes))
ax.set_xticks(tick_marks)
ax.set_xticklabels(classes)
ax.set_yticks(tick_marks)
ax.set_yticklabels(classes)

# 在每个格子里写入数值
thresh = cm.max() / 2.
for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
    ax.text(j, i, format(cm[i, j], 'd'),
            ha='center', va='center',
            color='red', fontsize=12)

ax.set_ylabel('True')
ax.set_xlabel('Predicted')
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
plt.close()
# -------------------------------------


# 绘制并保存 ROC 曲线并计算 AUC
fpr, tpr, roc_auc = {}, {}, {}
for i in range(probs.shape[1]):
    fpr[i], tpr[i], _ = roc_curve(y_test == i, probs[:, i])
    roc_auc[i] = auc(fpr[i], tpr[i])

for cls, val in roc_auc.items():
    print(f"类别 {cls} AUC: {val:.2f}")
print(f"平均 AUC: {np.mean(list(roc_auc.values())):.2f}")

plt.figure()
for i, val in roc_auc.items():
    plt.plot(fpr[i], tpr[i], label=f'Class {i} (AUC={val:.2f})')
plt.plot([0,1], [0,1], linestyle='--')
plt.title('ROC 曲线')
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.legend(loc='lower right')
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'roc_curve.png'))
plt.close()