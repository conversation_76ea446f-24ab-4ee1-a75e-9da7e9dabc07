#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_json_fix.py
---------------
测试JSON序列化修复是否成功
"""

import numpy as np
import json
from pathlib import Path

def test_numpy_encoder():
    """测试NumPy编码器"""
    print("🧪 测试NumPy编码器")
    print("=" * 30)
    
    # 自定义JSON编码器，处理NumPy数据类型
    class NumpyEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.bool_):
                return bool(obj)
            return super().default(obj)
    
    # 测试数据
    test_data = {
        'int32_value': np.int32(42),
        'int64_value': np.int64(123),
        'float32_value': np.float32(3.14),
        'float64_value': np.float64(2.718),
        'bool_value': np.bool_(True),
        'array_value': np.array([1, 2, 3]),
        'normal_int': 10,
        'normal_float': 1.5,
        'normal_bool': False,
        'normal_string': "test"
    }
    
    try:
        # 测试序列化
        json_str = json.dumps(test_data, cls=NumpyEncoder, indent=2)
        print("✅ JSON序列化成功")
        
        # 测试反序列化
        parsed_data = json.loads(json_str)
        print("✅ JSON反序列化成功")
        
        # 验证数据类型
        print("\n📊 数据类型验证:")
        for key, value in parsed_data.items():
            print(f"  {key}: {type(value).__name__} = {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON处理失败: {e}")
        return False

def test_analyze_class_1_performance():
    """测试类别1性能分析函数"""
    print("\n🧪 测试类别1性能分析函数")
    print("=" * 40)
    
    try:
        # 导入函数
        import sys
        sys.path.append('.')
        from test import analyze_class_1_performance
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 100
        
        # 模拟真实标签（类别不平衡）
        y_true = np.random.choice([0, 1, 2, 3], size=n_samples, p=[0.6, 0.1, 0.2, 0.1])
        
        # 模拟预测结果（有一定准确性）
        y_pred = y_true.copy()
        # 添加一些错误预测
        error_indices = np.random.choice(n_samples, size=20, replace=False)
        y_pred[error_indices] = np.random.choice([0, 1, 2, 3], size=20)
        
        # 模拟预测概率
        y_proba = np.random.dirichlet([1, 1, 1, 1], size=n_samples)
        
        print(f"📊 测试数据:")
        print(f"  样本数: {n_samples}")
        print(f"  真实标签分布: {dict(zip(*np.unique(y_true, return_counts=True)))}")
        print(f"  预测标签分布: {dict(zip(*np.unique(y_pred, return_counts=True)))}")
        
        # 调用分析函数
        result = analyze_class_1_performance(y_true, y_pred, y_proba)
        
        print(f"\n✅ 函数调用成功")
        print(f"📊 返回结果类型: {type(result)}")
        print(f"📊 返回结果键: {list(result.keys())}")
        
        # 测试JSON序列化
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.bool_):
                    return bool(obj)
                return super().default(obj)
        
        json_str = json.dumps(result, cls=NumpyEncoder, indent=2)
        print(f"✅ 结果JSON序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_analysis_json():
    """测试完整分析结果的JSON序列化"""
    print("\n🧪 测试完整分析结果JSON序列化")
    print("=" * 40)
    
    try:
        # 模拟完整的分析结果
        import pandas as pd
        
        class_1_analysis = {
            "overall_performance": {
                "accuracy": np.float64(0.85),
                "macro_f1": np.float32(0.78),
                "weighted_f1": np.float64(0.82),
                "balanced_accuracy": np.float32(0.80)
            },
            "class_1_metrics": {
                'precision': np.float64(0.75),
                'recall': np.float32(0.68),
                'specificity': np.float64(0.92),
                'f1': np.float32(0.71),
                'accuracy': np.float64(0.85),
                'balanced_accuracy': np.float32(0.80),
                'true_positives': np.int32(15),
                'false_positives': np.int64(5),
                'false_negatives': np.int32(7),
                'true_negatives': np.int64(73),
                'total_samples': np.int32(100),
                'true_class_1_count': np.int32(22),
                'pred_class_1_count': np.int32(20),
                'suggestions': ["召回率偏低"]
            },
            "target_achievement": {
                "targets": {
                    'accuracy': {'value': 0.85, 'weight': 0.3},
                    'class_1_recall': {'value': 0.65, 'weight': 0.4},
                    'class_1_f1': {'value': 0.60, 'weight': 0.3}
                },
                "achievements": {
                    "accuracy": np.bool_(True),
                    "class_1_recall": np.bool_(True),
                    "class_1_f1": np.bool_(True)
                },
                "composite_score": np.float64(1.05)
            },
            "model_info": {
                "total_test_samples": np.int32(100),
                "prediction_timestamp": pd.Timestamp.now().isoformat(),
                "model_type": "stacking_ensemble"
            }
        }
        
        # 自定义JSON编码器
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.bool_):
                    return bool(obj)
                return super().default(obj)
        
        # 测试序列化
        json_str = json.dumps(class_1_analysis, cls=NumpyEncoder, indent=2, ensure_ascii=False)
        print("✅ 完整分析结果JSON序列化成功")
        
        # 保存到文件测试
        test_file = Path("test_analysis_result.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(class_1_analysis, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
        
        print(f"✅ 结果保存到文件成功: {test_file}")
        
        # 读取验证
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        print(f"✅ 文件读取验证成功")
        
        # 清理测试文件
        test_file.unlink()
        print(f"✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 JSON序列化修复测试")
    print("=" * 50)
    
    tests = [
        ("NumPy编码器", test_numpy_encoder),
        ("类别1性能分析", test_analyze_class_1_performance),
        ("完整分析JSON", test_full_analysis_json)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        success = test_func()
        results[test_name] = success
        
        if success:
            print(f"✅ {test_name} - 通过")
        else:
            print(f"❌ {test_name} - 失败")
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 30)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"🎉 所有测试都通过！JSON序列化问题已修复")
        print(f"\n💡 现在可以正常运行:")
        print(f"  python run.py --enable_smote --enable_pca")
    elif passed > 0:
        print(f"⚠️ 部分测试通过，基本功能正常")
    else:
        print(f"❌ 所有测试都失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
