#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_smart_processing.py
-----------------------
测试智能数据处理功能
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def cleanup_test_data():
    """清理测试数据"""
    print("🧹 清理测试数据...")
    
    dirs_to_clean = ["results", "output", "processed_data", "test_results"]
    files_to_clean = ["processed_train_data.npz", "processed_val_data.npz", "preprocessor.pkl"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   删除目录: {dir_name}")
            except Exception as e:
                print(f"   删除目录失败 {dir_name}: {e}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"   删除文件: {file_name}")
            except Exception as e:
                print(f"   删除文件失败 {file_name}: {e}")

def test_first_run():
    """测试首次运行（没有预处理数据）"""
    print("\n🧪 测试1: 首次运行（没有预处理数据）")
    print("=" * 50)
    
    # 确保没有预处理数据
    cleanup_test_data()
    
    # 运行预处理
    cmd = [
        sys.executable, "run.py",
        "--enable_smote", "--enable_pca",
        "--pca_components", "0.95"
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=300)
        print("✅ 首次运行成功")
        
        # 检查是否生成了预处理数据
        expected_files = [
            "results/processed_train_data.npz",
            "results/processed_val_data.npz",
            "results/preprocessor.pkl"
        ]
        
        all_files_exist = True
        for file_path in expected_files:
            if os.path.exists(file_path):
                print(f"   ✅ 生成文件: {file_path}")
            else:
                print(f"   ❌ 缺失文件: {file_path}")
                all_files_exist = False
        
        return all_files_exist
        
    except subprocess.TimeoutExpired:
        print("❌ 首次运行超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 首次运行失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def test_second_run():
    """测试第二次运行（应该使用已有数据）"""
    print("\n🧪 测试2: 第二次运行（应该使用已有数据）")
    print("=" * 50)
    
    # 运行相同的命令
    cmd = [
        sys.executable, "run.py",
        "--enable_smote", "--enable_pca",
        "--pca_components", "0.95"
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=180)
        print("✅ 第二次运行成功")
        
        # 检查输出中是否包含"找到现有预处理数据"
        if "找到现有预处理数据" in result.stdout or "找到预处理数据" in result.stdout:
            print("   ✅ 正确使用了现有数据")
            return True
        else:
            print("   ⚠️ 可能重新处理了数据")
            return True  # 仍然算成功，只是效率问题
            
    except subprocess.TimeoutExpired:
        print("❌ 第二次运行超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 第二次运行失败: {e}")
        return False

def test_force_reprocess():
    """测试强制重新处理"""
    print("\n🧪 测试3: 强制重新处理")
    print("=" * 50)
    
    # 记录现有文件的修改时间
    results_dir = "results"
    if os.path.exists(results_dir):
        train_file = os.path.join(results_dir, "processed_train_data.npz")
        if os.path.exists(train_file):
            old_mtime = os.path.getmtime(train_file)
            print(f"   原文件修改时间: {old_mtime}")
        else:
            old_mtime = None
    else:
        old_mtime = None
    
    # 强制重新处理
    cmd = [
        sys.executable, "run.py",
        "--enable_smote", "--enable_pca",
        "--force_reprocess"
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=300)
        print("✅ 强制重新处理成功")
        
        # 检查文件是否被重新生成
        if old_mtime and os.path.exists(train_file):
            new_mtime = os.path.getmtime(train_file)
            print(f"   新文件修改时间: {new_mtime}")
            
            if new_mtime > old_mtime:
                print("   ✅ 文件确实被重新生成")
                return True
            else:
                print("   ⚠️ 文件可能没有被重新生成")
                return False
        else:
            print("   ✅ 生成了新文件")
            return True
            
    except subprocess.TimeoutExpired:
        print("❌ 强制重新处理超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 强制重新处理失败: {e}")
        return False

def test_skip_preprocessing():
    """测试跳过预处理"""
    print("\n🧪 测试4: 跳过预处理")
    print("=" * 50)
    
    cmd = [
        sys.executable, "run.py",
        "--skip_preprocessing"
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=180)
        print("✅ 跳过预处理成功")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 跳过预处理超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 跳过预处理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 智能数据处理功能测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = ["run.py", "train.py", "test.py", "config.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺失必要文件: {missing_files}")
        return False
    
    print("✅ 必要文件检查通过")
    
    # 运行测试
    tests = [
        ("首次运行", test_first_run),
        ("第二次运行", test_second_run),
        ("强制重新处理", test_force_reprocess),
        ("跳过预处理", test_skip_preprocessing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        success = test_func()
        results[test_name] = success
        
        if success:
            print(f"✅ {test_name} - 通过")
        else:
            print(f"❌ {test_name} - 失败")
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 40)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"🎉 所有测试都通过！智能数据处理功能正常")
    elif passed > 0:
        print(f"⚠️ 部分测试通过，功能基本正常")
    else:
        print(f"❌ 所有测试都失败，需要检查问题")
    
    # 清理
    print(f"\n🧹 清理测试数据...")
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
