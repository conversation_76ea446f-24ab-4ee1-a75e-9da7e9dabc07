import os
import numpy as np
import pandas as pd
import logging
from imblearn.under_sampling import TomekLinks, RandomUnderSampler
from imblearn.over_sampling import ADASYN
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import OrdinalEncoder
from sklearn.preprocessing import StandardScaler

from preprocessing import save_csv_if_exists

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def process_batch(data: pd.DataFrame) -> pd.DataFrame:
    """处理整个数据集：时间字段拆分、风险评分、类别与数值特征自动编码、采样、标准化及 PCA 降维"""

    # 列重命名：如果存在 'Severity' 列，则将其重命名为 'RISK_LEVEL'
    if 'Severity' in data.columns:
        data.rename(columns={'Severity': 'RISK_LEVEL'}, inplace=True)

    # 时间字段拆分（包含日期和时间）
    for col in ['Start_Time', 'End_Time']:
        data[col] = pd.to_datetime(data[col], errors='coerce')
        data[f'{col}_Year']   = data[col].dt.year
        data[f'{col}_Month']  = data[col].dt.month
        data[f'{col}_Day']    = data[col].dt.day
        data[f'{col}_Hour']   = data[col].dt.hour
        data[f'{col}_Minute'] = data[col].dt.minute
        data[f'{col}_Second'] = data[col].dt.second

    # 计算事故持续时长（分钟）
    data['Duration'] = (data['End_Time'] - data['Start_Time']).dt.total_seconds() / 60
    data['Duration'].fillna(data['Duration'].median(), inplace=True)

    # 划分“时段”
    data['Part_of_Day'] = pd.cut(
        data['Start_Time'].dt.hour,
        bins=[0, 6, 12, 18, 24],
        labels=['Night', 'Morning', 'Afternoon', 'Evening'],
        include_lowest=True
    ).astype(str).fillna('Unknown')

    logging.info("Time features, Duration and Part_of_Day created.")

    # 分离目标变量并调整为 0 起始
    y = data.pop('RISK_LEVEL').astype(int) - 1

    # ———— 类别特征整数编码 ————
    cat_cols = data.select_dtypes(include=['object', 'category']).columns.tolist()
    if cat_cols:
        ord_enc = OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1)
        cat_encoded = ord_enc.fit_transform(data[cat_cols]).astype(float)
        cat_encoded_cols = [f"{col}_ord" for col in cat_cols]
    else:
        cat_encoded = np.empty((len(data), 0))
        cat_encoded_cols = []

    # ———— 数值特征拼接 ————
    num_cols = data.select_dtypes(include=[np.number]).columns.tolist()
    num_data = data[num_cols].values
    X_df = pd.DataFrame(
        np.hstack([cat_encoded, num_data]),
        columns=cat_encoded_cols + num_cols
    )

    logging.info(f"Encoded features: {len(cat_encoded_cols)} categorical, {len(num_cols)} numerical.")

    # ———— 填充残留 NaN ————
    X_df.fillna(X_df.median(), inplace=True)

    # ———— 样本平衡流程 ————
    rus = RandomUnderSampler(random_state=42)
    X_res, y_res = rus.fit_resample(X_df, y)

    imputer2 = SimpleImputer(strategy='median')
    X_res = imputer2.fit_transform(X_res)

    ada = ADASYN(random_state=42)
    X_res, y_res = ada.fit_resample(X_res, y_res)

    tl = TomekLinks()
    X_res, y_res = tl.fit_resample(X_res, y_res)

    logging.info(f"Resampled dataset size: {X_res.shape[0]}")

    # ———— 使用所有特征，无特征选择 ————
    X_sel = X_res
    sel_cols = cat_encoded_cols + num_cols
    # ———— 标准化 ————
    X_scaled = StandardScaler().fit_transform(X_sel)
    # 直接保留所有编码特征
    result = pd.DataFrame(X_scaled, columns=sel_cols)
    result['RISK_LEVEL'] = y_res
    return result


def clean_data(data: pd.DataFrame) -> pd.DataFrame:
    drop_cols = [
        "Source", "ID",
        "Start_Lat", "Start_Lng", "End_Lat", "End_Lng",
        "Description", "Street", "City", "County", "State", "Zipcode", "Country",
        "Airport_Code", "Timezone", "Weather_Timestamp"
    ]
    data.drop(columns=[col for col in drop_cols if col in data.columns], inplace=True)
    """填充缺失值并处理异常（针对天气及通用字段）"""
    # 替换无限值为 NaN
    data.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 类别字段：天气、时段等，用众数或 'Unknown' 填充
    cat_fill = ['Weather_Condition', 'Sunrise_Sunset', 'Part_of_Day']
    for col in cat_fill:
        if col in data.columns:
            data[col].fillna(data[col].mode()[0], inplace=True)

    # 数值字段用中位数填充
    num_cols = [
        'Precipitation(in)', 'Wind_Speed(mph)', 'Visibility(mi)',
        'Pressure(in)', 'Humidity(%)', 'Wind_Chill(F)', 'Temperature(F)', 'Duration'
    ]
    for col in num_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
            data[col].fillna(data[col].median(), inplace=True)

    logging.info("Weather and numeric cleaning completed.")
    return data

def data_processing(file_path: str, output_dir: str):
    """
    加载 CSV 数据集，进行清洗、特征工程、编码及 PCA 降维，
    最后保存训练/验证/测试集。
    """
    logging.info("Starting data processing...")

    # 指定列类型并分块读取，降低内存占用
    dtypes = {
        'Severity': 'int8',
        'Temperature(F)': 'float32',
        'Humidity(%)': 'float32',
        'Pressure(in)': 'float32',
        'Visibility(mi)': 'float32',
        'Wind_Speed(mph)': 'float32',
        'Precipitation(in)': 'float32',
        'Distance(mi)': 'float32'
    }
    data = pd.read_csv(file_path, dtype=dtypes, low_memory=True)
    data.replace('', pd.NA, inplace=True)

    # 清洗数据
    data = clean_data(data)

    # 特征处理与降维
    processed_data = process_batch(data)

    # 划分训练/验证/测试集
    X_train, X_temp = train_test_split(processed_data, test_size=0.2, random_state=42)
    X_val, X_test   = train_test_split(X_temp, test_size=0.5, random_state=42)

    # 保存
    save_csv_if_exists(os.path.join(output_dir, "train_data.csv"), X_train)
    save_csv_if_exists(os.path.join(output_dir, "val_data.csv"), X_val)
    save_csv_if_exists(os.path.join(output_dir, "test_data.csv"), X_test)

    logging.info(f"Data processing completed. Outputs in {output_dir}")

if __name__ == '__main__':
    file_path  = 'data_2/US_Accidents_March23.csv'
    output_dir = 'data_2/'
    data_processing(file_path, output_dir)
