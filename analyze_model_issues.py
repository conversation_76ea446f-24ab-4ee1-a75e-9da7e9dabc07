#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
analyze_model_issues.py
----------------------
分析当前模型的问题，特别是类别1召回率低的原因
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from config import config
from tree_attention_pipeline import load_all, load_csv

def analyze_confusion_matrix(y_true, y_pred, class_names=None):
    """分析混淆矩阵"""
    if class_names is None:
        class_names = [f"类别{i}" for i in range(len(np.unique(y_true)))]
    
    cm = confusion_matrix(y_true, y_pred)
    
    print("🔍 混淆矩阵分析:")
    print("=" * 50)
    
    # 打印混淆矩阵
    print("混淆矩阵:")
    df_cm = pd.DataFrame(cm, index=class_names, columns=class_names)
    print(df_cm)
    
    print("\n📊 各类别错误分析:")
    for i, class_name in enumerate(class_names):
        total_true = cm[i].sum()  # 真实标签为i的总数
        correct = cm[i, i]        # 正确预测为i的数量
        
        print(f"\n{class_name} (真实样本数: {total_true}):")
        print(f"  正确预测: {correct} ({correct/total_true*100:.1f}%)")
        
        # 分析被误分类到哪些类别
        for j, other_class in enumerate(class_names):
            if i != j and cm[i, j] > 0:
                error_rate = cm[i, j] / total_true * 100
                print(f"  误分类为{other_class}: {cm[i, j]} ({error_rate:.1f}%)")
    
    return cm

def analyze_class_1_issues(y_true, y_pred):
    """专门分析类别1的问题"""
    print("\n🎯 类别1详细分析:")
    print("=" * 50)
    
    # 找出类别1的样本
    class_1_mask = (y_true == 1)
    class_1_true = y_true[class_1_mask]
    class_1_pred = y_pred[class_1_mask]
    
    total_class_1 = len(class_1_true)
    correct_class_1 = np.sum(class_1_pred == 1)
    
    print(f"类别1总样本数: {total_class_1}")
    print(f"正确识别数: {correct_class_1}")
    print(f"召回率: {correct_class_1/total_class_1*100:.1f}%")
    
    # 分析类别1被误分类的情况
    print("\n类别1被误分类的分布:")
    unique, counts = np.unique(class_1_pred, return_counts=True)
    for cls, count in zip(unique, counts):
        if cls != 1:
            percentage = count / total_class_1 * 100
            print(f"  误分类为类别{cls}: {count} ({percentage:.1f}%)")
    
    return class_1_mask, class_1_pred

def analyze_feature_importance():
    """分析特征重要性（如果可能的话）"""
    print("\n📈 特征重要性分析:")
    print("=" * 50)
    
    try:
        cfg = config
        paths = cfg["paths"]
        
        # 加载模型
        xgb_model, cat_model, stacking_model, cls_w = load_all(paths)
        
        print("🌳 XGBoost特征重要性 (前10个):")
        if hasattr(xgb_model, 'feature_importances_'):
            importance = xgb_model.feature_importances_
            top_indices = np.argsort(importance)[-10:][::-1]
            for i, idx in enumerate(top_indices):
                print(f"  特征{idx}: {importance[idx]:.4f}")
        
        print("\n🐱 CatBoost特征重要性 (前10个):")
        if hasattr(cat_model, 'get_feature_importance'):
            importance = cat_model.get_feature_importance()
            top_indices = np.argsort(importance)[-10:][::-1]
            for i, idx in enumerate(top_indices):
                print(f"  特征{idx}: {importance[idx]:.4f}")
                
    except Exception as e:
        print(f"无法分析特征重要性: {e}")

def suggest_improvements(cm, y_true, y_pred):
    """基于分析结果提出改进建议"""
    print("\n💡 改进建议:")
    print("=" * 50)
    
    # 计算各类别的召回率
    recalls = []
    for i in range(len(cm)):
        recall = cm[i, i] / cm[i].sum() if cm[i].sum() > 0 else 0
        recalls.append(recall)
    
    # 找出召回率最低的类别
    worst_class = np.argmin(recalls)
    worst_recall = recalls[worst_class]
    
    print(f"🎯 召回率最低的类别: 类别{worst_class} ({worst_recall*100:.1f}%)")
    
    if worst_class == 1:
        print("\n针对类别1召回率低的具体建议:")
        print("1. 🔥 大幅增加类别1的权重 (当前配置已调整)")
        print("2. 🎯 使用Focal Loss重点关注类别1")
        print("3. 📊 分析类别1的特征分布，是否与其他类别重叠")
        print("4. 🚀 考虑使用SMOTE等过采样技术")
        print("5. 🔧 调整决策阈值，降低类别1的分类阈值")
        print("6. 📈 增加更多对类别1有区分度的特征")
    
    # 分析类别间的混淆情况
    print(f"\n📊 主要混淆情况:")
    for i in range(len(cm)):
        for j in range(len(cm)):
            if i != j and cm[i, j] > 0:
                confusion_rate = cm[i, j] / cm[i].sum() * 100
                if confusion_rate > 10:  # 只显示混淆率超过10%的情况
                    print(f"  类别{i} -> 类别{j}: {confusion_rate:.1f}%")

def main():
    """主分析函数"""
    print("🔍 模型问题分析")
    print("=" * 60)
    
    try:
        cfg = config
        paths = cfg["paths"]
        label_col = cfg["task"]["label_col"]
        
        # 加载测试数据
        print("📂 加载测试数据...")
        X_ts, y_ts, _ = load_csv(paths["test_data"], label_col)
        
        # 加载模型
        print("📂 加载模型...")
        xgb_model, cat_model, stacking_model, cls_w = load_all(paths)
        
        # 创建堆叠融合预测器
        from tree_attention_pipeline import StackingFusionWrapper
        wrapper = StackingFusionWrapper(xgb_model, cat_model, stacking_model)
        
        # 进行预测
        print("🔮 进行预测...")
        y_pred_proba = wrapper.predict_proba(X_ts)
        y_pred = wrapper.predict(X_ts)
        
        # 打印分类报告
        print("\n📋 当前模型性能:")
        print(classification_report(y_ts, y_pred, digits=2))
        
        # 分析混淆矩阵
        cm = analyze_confusion_matrix(y_ts, y_pred)
        
        # 专门分析类别1
        analyze_class_1_issues(y_ts, y_pred)
        
        # 分析特征重要性
        analyze_feature_importance()
        
        # 提出改进建议
        suggest_improvements(cm, y_ts, y_pred)
        
        print("\n✅ 分析完成！")
        print("请根据以上分析结果运行 train_improved.py 来训练改进的模型。")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
