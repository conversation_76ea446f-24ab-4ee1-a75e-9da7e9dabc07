#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_anti_overfit.py
------------------
使用防过拟合配置运行训练
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def backup_current_config():
    """备份当前配置"""
    if os.path.exists("config.py"):
        backup_path = "config_backup.py"
        import shutil
        shutil.copy("config.py", backup_path)
        print(f"✅ 当前配置已备份到: {backup_path}")
        return backup_path
    return None

def apply_anti_overfit_config():
    """应用防过拟合配置"""
    if os.path.exists("config_anti_overfit.py"):
        import shutil
        shutil.copy("config_anti_overfit.py", "config.py")
        print(f"✅ 已应用防过拟合配置")
        return True
    else:
        print(f"❌ 防过拟合配置文件不存在")
        return False

def restore_config(backup_path):
    """恢复原配置"""
    if backup_path and os.path.exists(backup_path):
        import shutil
        shutil.copy(backup_path, "config.py")
        print(f"✅ 配置已恢复")

def run_training_with_monitoring():
    """运行带监控的训练"""
    print("🚀 开始防过拟合训练")
    print("=" * 50)
    
    # 运行训练
    cmd = [
        sys.executable, "run.py",
        "--enable_smote", "--enable_pca",
        "--pca_components", "0.90"  # 降低PCA保留比例
    ]
    
    print("📋 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=1800)
        print("✅ 训练完成")
        
        # 分析输出中的过拟合信息
        if "过拟合" in result.stdout:
            print("⚠️ 检测到过拟合警告")
        
        return True, result.stdout
        
    except subprocess.TimeoutExpired:
        print("❌ 训练超时")
        return False, "训练超时"
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return False, e.stderr if e.stderr else str(e)

def analyze_results():
    """分析训练结果"""
    print("\n📊 分析训练结果")
    print("=" * 30)
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("❌ 未找到results目录")
        return
    
    # 查找分析文件
    analysis_file = os.path.join(results_dir, "class_1_analysis.json")
    if os.path.exists(analysis_file):
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis = json.load(f)
        
        # 提取关键指标
        if 'overall_performance' in analysis:
            perf = analysis['overall_performance']
            accuracy = perf.get('accuracy', 0)
            macro_f1 = perf.get('macro_f1', 0)
            
            print(f"📈 整体性能:")
            print(f"   准确率: {accuracy:.4f}")
            print(f"   宏平均F1: {macro_f1:.4f}")
        
        if 'class_1_metrics' in analysis:
            class_1 = analysis['class_1_metrics']
            precision = class_1.get('precision', 0)
            recall = class_1.get('recall', 0)
            f1 = class_1.get('f1', 0)
            
            print(f"🎯 类别1性能:")
            print(f"   精确率: {precision:.4f}")
            print(f"   召回率: {recall:.4f}")
            print(f"   F1分数: {f1:.4f}")
        
        # 评估改进效果
        if 'target_achievement' in analysis:
            targets = analysis['target_achievement']
            composite_score = targets.get('composite_score', 0)
            
            print(f"🏆 综合评分: {composite_score:.3f}")
            
            if composite_score >= 1.0:
                print("🌟 优秀 - 防过拟合效果良好")
            elif composite_score >= 0.8:
                print("✅ 良好 - 有一定改进")
            else:
                print("⚠️ 仍需优化")
    
    else:
        print("❌ 未找到分析文件")

def compare_with_previous():
    """与之前结果比较"""
    print("\n🔍 与之前结果比较")
    print("=" * 30)
    
    # 这里可以实现与之前结果的比较逻辑
    print("💡 建议:")
    print("1. 如果测试性能有提升，说明防过拟合有效")
    print("2. 如果训练性能下降但测试性能提升，这是正常的")
    print("3. 如果两者都下降，可能需要调整其他参数")

def main():
    """主函数"""
    print("🛡️ 防过拟合训练工具")
    print("=" * 60)
    
    # 1. 诊断当前问题
    print("🔬 运行过拟合诊断...")
    try:
        subprocess.run([sys.executable, "diagnose_overfitting.py"], check=True)
    except:
        print("⚠️ 诊断脚本运行失败，继续训练")
    
    # 2. 备份当前配置
    backup_path = backup_current_config()
    
    try:
        # 3. 应用防过拟合配置
        if not apply_anti_overfit_config():
            return
        
        # 4. 运行训练
        success, output = run_training_with_monitoring()
        
        if success:
            # 5. 分析结果
            analyze_results()
            
            # 6. 比较效果
            compare_with_previous()
            
            print(f"\n🎉 防过拟合训练完成!")
            print(f"📊 查看详细结果: results/class_1_analysis.json")
            print(f"📈 查看学习曲线: learning_curves.png")
            
        else:
            print(f"\n❌ 训练失败")
            print(f"错误信息: {output}")
    
    finally:
        # 7. 恢复原配置
        if backup_path:
            restore_config(backup_path)
    
    print(f"\n💡 下一步建议:")
    print(f"1. 比较防过拟合前后的测试性能")
    print(f"2. 如果效果好，可以进一步调优参数")
    print(f"3. 如果效果不佳，考虑其他方法:")
    print(f"   - 增加更多真实数据")
    print(f"   - 使用更强的正则化")
    print(f"   - 尝试不同的模型架构")

if __name__ == "__main__":
    main()
