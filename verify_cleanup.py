#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
verify_cleanup.py
-----------------
验证所有注意力相关代码是否已完全清理
"""

import os
import re
from pathlib import Path

def check_file_for_patterns(file_path, patterns):
    """检查文件中是否包含指定模式"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        for pattern_name, pattern in patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                # 找到匹配的行号
                line_num = content[:match.start()].count('\n') + 1
                line_content = lines[line_num - 1].strip()
                
                # 跳过注释行和已删除的标记
                if (line_content.startswith('#') or 
                    '删除了' in line_content or 
                    'deleted' in line_content.lower()):
                    continue
                    
                issues.append({
                    'file': file_path,
                    'line': line_num,
                    'pattern': pattern_name,
                    'content': line_content
                })
                
    except Exception as e:
        print(f"⚠️ 无法读取文件 {file_path}: {e}")
        
    return issues

def verify_cleanup():
    """验证清理是否完整"""
    print("🔍 验证注意力相关代码清理情况")
    print("=" * 50)
    
    # 定义要检查的模式
    attention_patterns = {
        'PerformerSelfAttention': r'PerformerSelfAttention',
        'EnhancedLeafTransformer': r'EnhancedLeafTransformer',
        'TreeAttentionPipeline': r'TreeAttentionPipeline',
        'build_tokens': r'build_tokens(?!.*删除)',
        'auto_fusion_weights': r'auto_fusion_weights',
        'FusionWrapper': r'FusionWrapper(?!.*删除)',
        '_calculate_class_separation': r'_calculate_class_separation',
        'combined_augmentation': r'combined_augmentation',
        'smart_depth_perturbation': r'smart_depth_perturbation',
        'ultimate_token_augmentation': r'ultimate_token_augmentation',
        '_identify_minority_classes': r'_identify_minority_classes',
        'create_class_3_aware_loss': r'create_class_3_aware_loss'
    }
    
    # 要检查的文件
    files_to_check = [
        'tree_attention_pipeline.py',
        'train.py',
        'train_improved.py',
        'test.py',
        'analyze_model_issues.py'
    ]
    
    all_issues = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📂 检查文件: {file_path}")
            issues = check_file_for_patterns(file_path, attention_patterns)
            
            if issues:
                print(f"  ❌ 发现 {len(issues)} 个问题:")
                for issue in issues:
                    print(f"    行 {issue['line']}: {issue['pattern']} - {issue['content']}")
                all_issues.extend(issues)
            else:
                print(f"  ✅ 清理完成")
        else:
            print(f"  ⚠️ 文件不存在: {file_path}")
    
    # 总结
    print(f"\n📊 清理验证总结:")
    print(f"   检查文件数: {len(files_to_check)}")
    print(f"   发现问题数: {len(all_issues)}")
    
    if all_issues:
        print(f"\n❌ 仍有残留代码需要清理:")
        for issue in all_issues:
            print(f"   {issue['file']}:{issue['line']} - {issue['pattern']}")
        return False
    else:
        print(f"\n✅ 所有注意力相关代码已完全清理!")
        return True

def check_imports():
    """检查导入是否正确"""
    print(f"\n🔍 检查导入情况")
    print("-" * 30)
    
    try:
        # 测试基本导入
        from tree_attention_pipeline import (
            train_xgb, train_cat, train_stacking_ensemble,
            StackingFusionWrapper, SimpleStackingWrapper,
            infer_class_weight, save_all, load_all
        )
        print("✅ 核心函数导入成功")
        
        # 测试堆叠类
        stacking = SimpleStackingWrapper()
        print("✅ 堆叠类实例化成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def check_stacking_functionality():
    """检查堆叠功能是否正常"""
    print(f"\n🔍 检查堆叠功能")
    print("-" * 30)
    
    try:
        import numpy as np
        from sklearn.datasets import make_classification
        from tree_attention_pipeline import (
            train_xgb, train_cat, train_stacking_ensemble,
            StackingFusionWrapper, infer_class_weight
        )
        
        # 创建测试数据
        X, y = make_classification(
            n_samples=100, n_features=10, n_classes=4,
            n_informative=8, random_state=42
        )
        
        # 计算类别权重
        cls_w = infer_class_weight(y, "balanced")
        
        # 训练基模型
        xgb_config = {"n_estimators": 10, "max_depth": 3, "random_state": 42}
        cat_config = {"iterations": 10, "depth": 3, "random_state": 42, "verbose": False}
        
        xgb_model = train_xgb(X, y, cls_w, xgb_config)
        cat_model = train_cat(X, y, cls_w, cat_config)
        
        # 训练堆叠模型
        stacking_model = train_stacking_ensemble(
            X, y, xgb_model, cat_model, cv_folds=3, random_state=42
        )
        
        # 创建融合包装器
        wrapper = StackingFusionWrapper(xgb_model, cat_model, stacking_model)
        
        # 测试预测
        proba = wrapper.predict_proba(X[:10])
        pred = wrapper.predict(X[:10])
        
        print("✅ 堆叠功能测试成功")
        print(f"   预测概率形状: {proba.shape}")
        print(f"   预测结果形状: {pred.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 堆叠功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证代码清理情况")
    print("=" * 60)
    
    # 1. 验证代码清理
    cleanup_ok = verify_cleanup()
    
    # 2. 检查导入
    import_ok = check_imports()
    
    # 3. 检查功能
    function_ok = check_stacking_functionality()
    
    # 总结
    print(f"\n🎯 最终验证结果:")
    print(f"   代码清理: {'✅ 通过' if cleanup_ok else '❌ 失败'}")
    print(f"   导入检查: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"   功能测试: {'✅ 通过' if function_ok else '❌ 失败'}")
    
    if cleanup_ok and import_ok and function_ok:
        print(f"\n🎉 所有验证通过！代码重构成功完成！")
        return True
    else:
        print(f"\n⚠️ 部分验证失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
