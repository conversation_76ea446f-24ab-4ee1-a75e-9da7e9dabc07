# -*- coding: utf-8 -*-
"""
config.py
---------
集中所有配置；训练/评估脚本与模型流水线统一读取本字典。
"""

from pathlib import Path

# 项目根路径（自动推断当前文件所在目录）
PROJECT_ROOT = Path(__file__).resolve().parent

config = {
    # ========= 全局 =========
    "global": {
        "seed": 42,
        "class_weight": {0: 1.0, 1: 4.0, 2: 2.0, 3: 1.5},  # 🛡️ 防过拟合：降低类别1权重
        "verbose_data_info": True,
    },

    # ========= 数据路径 =========
    "paths": {
        "train_data": str(PROJECT_ROOT / "data_2" / "train_data.csv"),
        "val_data": str(PROJECT_ROOT / "data_2" / "val_data.csv"),
        "test_data": str(PROJECT_ROOT / "data_2" / "test_data.csv"),

        # 模型保存
        "xgb_model_path": str(PROJECT_ROOT / "results" / "xgb_model.json"),
        "cat_model_path": str(PROJECT_ROOT / "results" / "cat_model.cbm"),
        "stacking_model": str(PROJECT_ROOT / "results" / "stacking_model.pkl"),
        "class_weights": str(PROJECT_ROOT / "results" / "class_weights.pkl"),
        "result_dir": str(PROJECT_ROOT / "results"),
    },

    # ========= 任务信息 =========
    # num_class 可在运行时根据训练数据自动推断；若已知可提前填入
    "task": {
        "label_col": "RISK_LEVEL",
        "num_class": None,  # None -> 自动
    },

    # ========= 🛡️ 防过拟合的XGBoost配置 =========
    "xgb": {
        "objective": "multi:softprob",
        "num_class": None,  # 与 task.num_class 同步
        "eval_metric": "mlogloss",
        "random_state": 42,
        # 🛡️ 防过拟合参数
        "n_estimators": 500,        # ⬇️ 减少树数量
        "max_depth": 4,             # ⬇️ 降低树深度
        "learning_rate": 0.08,      # ⬆️ 适中学习率
        "subsample": 0.75,          # ⬇️ 降低采样率
        "colsample_bytree": 0.75,   # ⬇️ 特征采样
        "colsample_bylevel": 0.75,  # ⬇️ 每层特征采样
        "colsample_bynode": 0.75,   # ⬇️ 每节点特征采样
        # 🔧 正则化参数
        "gamma": 2.0,               # ⬆️ 增加最小分裂损失
        "reg_alpha": 3.0,           # ⬆️ L1正则化
        "reg_lambda": 5.0,          # ⬆️ L2正则化
        "min_child_weight": 5,      # ⬆️ 叶子节点最小权重
        "tree_method": "hist",      # CPU训练更稳定
        "max_delta_step": 0,        # 保守步长
    },

    # ========= 🛡️ 防过拟合的CatBoost配置 =========
    "cat": {
        "loss_function": "MultiClass",
        "eval_metric": "MultiClass",
        "random_seed": 42,
        "verbose": False,
        # 🛡️ 防过拟合参数
        "iterations": 800,          # ⬇️ 减少迭代次数
        "depth": 4,                 # ⬇️ 降低深度
        "learning_rate": 0.08,      # ⬆️ 适中学习率
        # 🔧 正则化参数
        "l2_leaf_reg": 10.0,        # ⬆️ 增加L2正则化
        "random_strength": 2.0,     # ⬆️ 增加随机性
        "bagging_temperature": 0.5, # ⬇️ 降低bagging温度
        "border_count": 32,         # ⬇️ 减少边界数量
        # 🎯 采样和验证
        "subsample": 0.75,          # ⬇️ 降低采样率
        "bootstrap_type": "Bernoulli",
        "task_type": "CPU",         # CPU训练更稳定
        # class_weights 在训练函数中注入
    },

    # ========= 🛡️ 防过拟合的堆叠融合策略 =========
    "ensemble": {
        "use_stacking": True,   # 使用堆叠融合
        "meta_learner": "lightgbm",  # 元学习器
        "cv_folds": 5,          # 5折交叉验证
        "random_state": 42,
        # 🛡️ 防过拟合的元学习器参数
        "meta_params": {
            "n_estimators": 100,    # 较少的树
            "max_depth": 3,         # 浅层树
            "learning_rate": 0.1,   # 适中学习率
            "subsample": 0.8,
            "colsample_bytree": 0.8,
            "reg_alpha": 1.0,
            "reg_lambda": 1.0,
            "random_state": 42
        }
    },

    # ========= 🛡️ 防过拟合验证配置 =========
    "validation": {
        "cv_folds": 5,
        "stratify": True,
        "shuffle": True,
        "random_state": 42,
        # 🎯 过拟合监控
        "monitor_overfitting": True,
        "train_val_gap_threshold": 0.1,  # 训练验证差距阈值
        "early_stopping_patience": 3,
    },

    # ========= 测试/绘图 =========
    "test": {
        "max_tokens": 1024,
        "plots": {
            "roc": True,
            "confusion": True,
            "precision_recall": True,  # 新增PR曲线
            "class_distribution": True, # 新增类别分布图
        },
    },

    # ========= 新增：终极优化配置 =========
    "ultimate": {
        "mixed_precision": False,      # 保持关闭，避免数值不稳定
        "advanced_augmentation": True, # 启用高级数据增强
        "progressive_training": True,  # 渐进式训练
        "early_stopping": {
            "monitor": "val_weighted_accuracy",
            "patience": 15,
            "restore_best_weights": True,
            "min_delta": 0.001,
        },
        # 🎯 类别平衡策略
        "class_balance_strategy": {
            "use_smote": True,           # 使用SMOTE过采样
            "smote_k_neighbors": 5,
            "use_class_weights": True,
            "dynamic_weight_adjustment": True,
        },
    },
}


def sync_num_class(cfg, num_class: int):
    """
    在读取数据后调用，将 task/模型部分的 num_class 同步。
    """
    cfg["task"]["num_class"] = num_class
    cfg["xgb"]["num_class"] = num_class
    # CatBoost 不强制; loss_function=MultiClass 会自动识别
    return cfg
