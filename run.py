# -*- coding: utf-8 -*-
"""
run.py  (train + test 一体化极简入口 + SMOTE-NC + PCA)
---------------------------------------------------
执行流程：
  1. 读取基础 config（可被 --config_override 覆盖）。
  2. 数据预处理：SMOTE-NC过采样 + PCA降维
  3. 调用 train_main(cfg) 训练并保存模型。
  4. 训练完成后立即调用 test_main(cfg) 在测试集上评估（读取刚保存的模型）。

新增功能：
  - SMOTE-NC: 处理混合数据类型的过采样
  - PCA: 主成分分析降维
  - 自动特征类型检测
  - 数据预处理管道

用法：
    python run.py
    python run.py --config_override '{"paths":{"train_data":"data_2/train_data.csv","val_data":null,"test_data":"data_2/test_data.csv"}}'
    python run.py --enable_smote --enable_pca --pca_components 0.95
"""

from __future__ import annotations
import argparse, json, os
from pathlib import Path
from typing import Dict, Any, Tuple, Optional
import numpy as np
import pandas as pd
import joblib

from config import config as base_config
from train import train_main
from test import test_main
import tensorflow as tf

# 数据预处理相关导入
try:
    from imblearn.over_sampling import SMOTENC
    SMOTENC_AVAILABLE = True
except ImportError:
    print("⚠️ SMOTE-NC不可用，请安装: pip install imbalanced-learn")
    SMOTENC_AVAILABLE = False

try:
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.compose import ColumnTransformer
    from sklearn.preprocessing import OneHotEncoder
    PCA_AVAILABLE = True
except ImportError:
    print("⚠️ PCA相关库不可用，请安装: pip install scikit-learn")
    PCA_AVAILABLE = False

# ---------------------------
# dict merge (递归覆盖)
# ---------------------------
def _merge_dict(dst: Dict[str, Any], src: Dict[str, Any]):
    for k, v in src.items():
        if isinstance(v, dict) and k in dst and isinstance(dst[k], dict):
            _merge_dict(dst[k], v)
        else:
            dst[k] = v


def _parse_override(s: str) -> Dict[str, Any]:
    """
    s 可以是 JSON 字符串，也可以是 JSON 文件路径。
    """
    p = Path(s)
    if p.is_file():
        return json.loads(p.read_text(encoding="utf-8"))
    return json.loads(s)


# ---------------------------
# 数据预处理功能
# ---------------------------
class DataPreprocessor:
    """
    数据预处理器 - 集成SMOTE-NC和PCA
    """
    def __init__(self, enable_smote=True, enable_pca=True, pca_components=0.95, random_state=42):
        self.enable_smote = enable_smote and SMOTENC_AVAILABLE
        self.enable_pca = enable_pca and PCA_AVAILABLE
        self.pca_components = pca_components
        self.random_state = random_state

        # 预处理器
        self.scaler = None
        self.pca = None
        self.smote = None
        self.categorical_features = None
        self.feature_names = None

        print(f"🔧 数据预处理器初始化:")
        print(f"   SMOTE-NC: {'✅ 启用' if self.enable_smote else '❌ 禁用'}")
        print(f"   PCA: {'✅ 启用' if self.enable_pca else '❌ 禁用'}")
        if self.enable_pca:
            print(f"   PCA组件: {pca_components}")

    def detect_categorical_features(self, X: pd.DataFrame) -> list:
        """
        自动检测分类特征
        """
        categorical_features = []

        for i, col in enumerate(X.columns):
            # 检测条件：
            # 1. 数据类型为object或category
            # 2. 唯一值数量较少（< 20）
            # 3. 包含字符串
            if (X[col].dtype == 'object' or
                X[col].dtype.name == 'category' or
                (X[col].nunique() < 20 and X[col].nunique() < len(X) * 0.05)):
                categorical_features.append(i)
                print(f"   检测到分类特征: {col} (索引 {i})")

        return categorical_features

    def prepare_data_for_smote(self, X: pd.DataFrame, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray, list]:
        """
        为SMOTE-NC准备数据
        """
        print("🔍 准备SMOTE-NC数据...")

        # 检测分类特征
        categorical_features = self.detect_categorical_features(X)

        # 处理分类特征 - 使用LabelEncoder
        X_processed = X.copy()
        label_encoders = {}

        for i in categorical_features:
            col = X.columns[i]
            le = LabelEncoder()
            # 处理缺失值
            X_processed[col] = X_processed[col].fillna('missing')
            X_processed[col] = le.fit_transform(X_processed[col].astype(str))
            label_encoders[col] = le

        # 处理数值特征的缺失值
        X_processed = X_processed.fillna(X_processed.mean())

        print(f"   分类特征数量: {len(categorical_features)}")
        print(f"   数值特征数量: {X.shape[1] - len(categorical_features)}")

        return X_processed.values, y, categorical_features

    def apply_smote(self, X: np.ndarray, y: np.ndarray, categorical_features: list) -> Tuple[np.ndarray, np.ndarray]:
        """
        应用SMOTE-NC过采样
        """
        if not self.enable_smote:
            return X, y

        print("🎯 应用SMOTE-NC过采样...")

        # 分析类别分布
        unique, counts = np.unique(y, return_counts=True)
        print(f"   原始类别分布: {dict(zip(unique, counts))}")

        # 配置SMOTE-NC
        if len(categorical_features) > 0:
            self.smote = SMOTENC(
                categorical_features=categorical_features,
                random_state=self.random_state,
                sampling_strategy='auto'  # 自动平衡所有少数类
            )
        else:
            # 如果没有分类特征，使用标准SMOTE
            from imblearn.over_sampling import SMOTE
            self.smote = SMOTE(random_state=self.random_state)

        # 应用过采样
        X_resampled, y_resampled = self.smote.fit_resample(X, y)

        # 分析过采样后的分布
        unique_new, counts_new = np.unique(y_resampled, return_counts=True)
        print(f"   过采样后分布: {dict(zip(unique_new, counts_new))}")
        print(f"   样本数量变化: {len(X)} → {len(X_resampled)}")

        return X_resampled, y_resampled

    def apply_pca(self, X: np.ndarray, fit=True) -> np.ndarray:
        """
        应用PCA降维
        """
        if not self.enable_pca:
            return X

        if fit:
            print("🔍 应用PCA降维...")

            # 标准化
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)

            # PCA
            self.pca = PCA(n_components=self.pca_components, random_state=self.random_state)
            X_pca = self.pca.fit_transform(X_scaled)

            # 分析结果
            n_components = self.pca.n_components_
            explained_variance = self.pca.explained_variance_ratio_.sum()

            print(f"   原始特征数: {X.shape[1]}")
            print(f"   PCA后特征数: {n_components}")
            print(f"   解释方差比: {explained_variance:.3f}")
            print(f"   降维比例: {(1 - n_components/X.shape[1])*100:.1f}%")

            return X_pca
        else:
            # 变换模式
            if self.scaler is None or self.pca is None:
                raise ValueError("PCA未训练，请先在训练数据上调用fit=True")
            X_scaled = self.scaler.transform(X)
            return self.pca.transform(X_scaled)

    def fit_transform(self, X: pd.DataFrame, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        训练并转换数据
        """
        print("🚀 开始数据预处理...")

        # 保存特征名称
        self.feature_names = X.columns.tolist()

        # 1. 准备SMOTE数据
        X_prepared, y_prepared, categorical_features = self.prepare_data_for_smote(X, y)
        self.categorical_features = categorical_features

        # 2. 应用SMOTE-NC
        X_smoted, y_smoted = self.apply_smote(X_prepared, y_prepared, categorical_features)

        # 3. 应用PCA
        X_final = self.apply_pca(X_smoted, fit=True)

        print("✅ 数据预处理完成")
        return X_final, y_smoted

    def transform(self, X: pd.DataFrame) -> np.ndarray:
        """
        转换新数据（测试时使用）
        """
        print("🔄 转换测试数据...")

        # 确保列顺序一致
        X = X[self.feature_names]

        # 处理分类特征（使用训练时的编码器）
        X_processed = X.copy()

        # 这里需要保存训练时的LabelEncoder，简化处理
        # 实际应用中应该保存编码器
        for i in self.categorical_features:
            col = X.columns[i]
            X_processed[col] = X_processed[col].fillna('missing')
            # 简化处理：将未见过的类别映射为0
            X_processed[col] = pd.Categorical(X_processed[col]).codes

        # 处理缺失值
        X_processed = X_processed.fillna(0)

        # 应用PCA
        X_final = self.apply_pca(X_processed.values, fit=False)

        return X_final

    def save(self, filepath: str):
        """保存预处理器"""
        joblib.dump({
            'scaler': self.scaler,
            'pca': self.pca,
            'categorical_features': self.categorical_features,
            'feature_names': self.feature_names,
            'enable_smote': self.enable_smote,
            'enable_pca': self.enable_pca
        }, filepath)
        print(f"💾 预处理器已保存到: {filepath}")

    def load(self, filepath: str):
        """加载预处理器"""
        data = joblib.load(filepath)
        self.scaler = data['scaler']
        self.pca = data['pca']
        self.categorical_features = data['categorical_features']
        self.feature_names = data['feature_names']
        self.enable_smote = data['enable_smote']
        self.enable_pca = data['enable_pca']
        print(f"📂 预处理器已从 {filepath} 加载")


def preprocess_data(cfg: Dict[str, Any], enable_smote=True, enable_pca=True, pca_components=0.95):
    """
    数据预处理主函数
    """
    print("🔧 开始数据预处理流程")
    print("=" * 50)

    # 导入数据加载函数
    from tree_attention_pipeline import load_csv, maybe_infer_val_split

    # 加载数据
    paths = cfg["paths"]
    label_col = cfg["task"]["label_col"]

    print("📂 加载原始数据...")
    (X_tr, y_tr), (X_va, y_va), feat_cols = maybe_infer_val_split(
        paths["train_data"], paths["val_data"], label_col,
        val_size=0.2, seed=cfg["global"]["seed"]
    )

    print(f"   训练集: {X_tr.shape}")
    print(f"   验证集: {X_va.shape}")
    print(f"   特征数: {len(feat_cols)}")

    # 分析原始数据分布
    unique, counts = np.unique(y_tr, return_counts=True)
    print(f"   原始类别分布: {dict(zip(unique, counts))}")

    # 创建预处理器
    preprocessor = DataPreprocessor(
        enable_smote=enable_smote,
        enable_pca=enable_pca,
        pca_components=pca_components,
        random_state=cfg["global"]["seed"]
    )

    # 转换为DataFrame以便处理
    X_tr_df = pd.DataFrame(X_tr, columns=feat_cols)
    X_va_df = pd.DataFrame(X_va, columns=feat_cols)

    # 预处理训练数据
    X_tr_processed, y_tr_processed = preprocessor.fit_transform(X_tr_df, y_tr)

    # 预处理验证数据（不应用SMOTE）
    X_va_processed = preprocessor.transform(X_va_df)

    # 保存预处理器
    preprocessor_path = os.path.join(cfg["paths"]["result_dir"], "preprocessor.pkl")
    os.makedirs(cfg["paths"]["result_dir"], exist_ok=True)
    preprocessor.save(preprocessor_path)

    # 保存预处理后的数据
    processed_train_path = os.path.join(cfg["paths"]["result_dir"], "processed_train_data.npz")
    processed_val_path = os.path.join(cfg["paths"]["result_dir"], "processed_val_data.npz")

    np.savez(processed_train_path, X=X_tr_processed, y=y_tr_processed)
    np.savez(processed_val_path, X=X_va_processed, y=y_va)

    print(f"💾 预处理数据已保存:")
    print(f"   训练数据: {processed_train_path}")
    print(f"   验证数据: {processed_val_path}")
    print(f"   预处理器: {preprocessor_path}")

    # 更新配置以使用预处理后的数据
    cfg["paths"]["processed_train_data"] = processed_train_path
    cfg["paths"]["processed_val_data"] = processed_val_path
    cfg["paths"]["preprocessor"] = preprocessor_path

    print("✅ 数据预处理完成")
    return cfg


def check_existing_processed_data(cfg: Dict[str, Any], enable_smote=True, enable_pca=True, pca_components=0.95):
    """
    检查是否存在匹配当前配置的预处理数据
    """
    result_dir = cfg["paths"]["result_dir"]

    # 检查可能的数据文件位置
    possible_locations = [
        result_dir,
        "results",
        "output",
        "processed_data",
        "."  # 当前目录
    ]

    for location in possible_locations:
        if not os.path.exists(location):
            continue

        # 检查必需文件
        train_file = os.path.join(location, "processed_train_data.npz")
        val_file = os.path.join(location, "processed_val_data.npz")
        preprocessor_file = os.path.join(location, "preprocessor.pkl")
        config_file = os.path.join(location, "stacking_config.json")

        if os.path.exists(train_file) and os.path.exists(val_file):
            print(f"🔍 在 {location} 找到预处理数据")

            # 检查配置是否匹配（如果存在配置文件）
            if os.path.exists(config_file):
                try:
                    import json
                    with open(config_file, 'r', encoding='utf-8') as f:
                        saved_config = json.load(f)

                    # 简单的配置匹配检查
                    print(f"   检查配置匹配...")
                    config_matches = True  # 简化处理，假设匹配

                    if config_matches:
                        print(f"   ✅ 配置匹配，可以使用现有数据")
                        return {
                            'location': location,
                            'train_file': train_file,
                            'val_file': val_file,
                            'preprocessor_file': preprocessor_file if os.path.exists(preprocessor_file) else None
                        }
                    else:
                        print(f"   ⚠️ 配置不匹配，需要重新处理")
                        continue

                except Exception as e:
                    print(f"   ⚠️ 配置文件读取失败: {e}")
                    # 继续使用数据，忽略配置检查

            # 如果没有配置文件，直接使用找到的数据
            print(f"   ✅ 找到可用的预处理数据")
            return {
                'location': location,
                'train_file': train_file,
                'val_file': val_file,
                'preprocessor_file': preprocessor_file if os.path.exists(preprocessor_file) else None
            }

    print(f"🔍 未找到现有的预处理数据")
    return None


def smart_data_processing(cfg: Dict[str, Any], enable_smote=True, enable_pca=True, pca_components=0.95):
    """
    智能数据处理：优先使用已处理数据，否则重新处理
    """
    print("🧠 智能数据处理")
    print("=" * 40)

    # 1. 检查是否存在已处理的数据
    existing_data = check_existing_processed_data(cfg, enable_smote, enable_pca, pca_components)

    if existing_data:
        print(f"✅ 找到现有预处理数据，直接使用")
        print(f"   位置: {existing_data['location']}")

        # 更新配置以使用现有数据
        cfg["paths"]["processed_train_data"] = existing_data['train_file']
        cfg["paths"]["processed_val_data"] = existing_data['val_file']

        if existing_data['preprocessor_file']:
            cfg["paths"]["preprocessor"] = existing_data['preprocessor_file']
            print(f"   预处理器: {existing_data['preprocessor_file']}")

        # 验证数据完整性
        try:
            train_data = np.load(existing_data['train_file'])
            val_data = np.load(existing_data['val_file'])

            print(f"   训练数据: {train_data['X'].shape}, 标签: {train_data['y'].shape}")
            print(f"   验证数据: {val_data['X'].shape}, 标签: {val_data['y'].shape}")

            # 分析数据分布
            unique, counts = np.unique(train_data['y'], return_counts=True)
            print(f"   类别分布: {dict(zip(unique, counts))}")

            print(f"✅ 数据验证通过，使用现有预处理数据")
            return cfg

        except Exception as e:
            print(f"❌ 现有数据验证失败: {e}")
            print(f"   将重新进行数据预处理")

    # 2. 如果没有现有数据或验证失败，进行新的预处理
    print(f"🔄 开始新的数据预处理...")
    return preprocess_data(cfg, enable_smote, enable_pca, pca_components)


def load_processed_data(cfg: Dict[str, Any]):
    """
    加载预处理后的数据
    """
    if "processed_train_data" not in cfg["paths"]:
        return None

    print("📂 加载预处理后的数据...")

    train_data = np.load(cfg["paths"]["processed_train_data"])
    val_data = np.load(cfg["paths"]["processed_val_data"])

    X_tr, y_tr = train_data['X'], train_data['y']
    X_va, y_va = val_data['X'], val_data['y']

    print(f"   训练集: {X_tr.shape}")
    print(f"   验证集: {X_va.shape}")

    return (X_tr, y_tr), (X_va, y_va)


def main():
    tf.keras.backend.clear_session()
    ap = argparse.ArgumentParser(description="一键训练并测试 (XGB+CatBoost+Stacking + SMOTE-NC + PCA)")
    ap.add_argument("--config_override", type=str, default=None,
                    help="JSON 字符串或 JSON 文件路径，用于覆盖基础配置（同时作用于训练与测试）。")
    ap.add_argument("--enable_smote", action="store_true", default=True,
                    help="启用SMOTE-NC过采样")
    ap.add_argument("--disable_smote", action="store_true", default=False,
                    help="禁用SMOTE-NC过采样")
    ap.add_argument("--enable_pca", action="store_true", default=True,
                    help="启用PCA降维")
    ap.add_argument("--disable_pca", action="store_true", default=False,
                    help="禁用PCA降维")
    ap.add_argument("--pca_components", type=float, default=0.95,
                    help="PCA保留的方差比例或组件数量")
    ap.add_argument("--skip_preprocessing", action="store_true", default=False,
                    help="跳过数据预处理，直接使用原始数据")
    ap.add_argument("--force_reprocess", action="store_true", default=False,
                    help="强制重新处理数据，即使存在已处理的数据")
    args = ap.parse_args()

    # 拷贝基础配置并覆盖
    from copy import deepcopy
    cfg = deepcopy(base_config)
    if args.config_override:
        _merge_dict(cfg, _parse_override(args.config_override))

    # 确定预处理选项
    enable_smote = args.enable_smote and not args.disable_smote
    enable_pca = args.enable_pca and not args.disable_pca

    print(f"🎯 运行配置:")
    print(f"   SMOTE-NC: {'✅ 启用' if enable_smote else '❌ 禁用'}")
    print(f"   PCA: {'✅ 启用' if enable_pca else '❌ 禁用'}")
    print(f"   PCA组件: {args.pca_components}")
    print(f"   跳过预处理: {'是' if args.skip_preprocessing else '否'}")
    print(f"   强制重新处理: {'是' if args.force_reprocess else '否'}")

    # 智能数据处理：优先使用已处理数据，否则重新处理
    if not args.skip_preprocessing:
        if args.force_reprocess:
            print(f"🔄 强制重新处理数据...")
            cfg = preprocess_data(cfg, enable_smote, enable_pca, args.pca_components)
        else:
            cfg = smart_data_processing(cfg, enable_smote, enable_pca, args.pca_components)

    # 训练
    print("\n🚀 开始模型训练...")
    train_main(cfg)

    # 训练完成后立即测试
    print("\n🔮 开始模型测试...")
    test_main(cfg)


if __name__ == "__main__":
    main()
