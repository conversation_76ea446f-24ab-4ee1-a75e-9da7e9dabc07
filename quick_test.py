#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
quick_test.py
------------
快速测试修复是否成功
"""

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试导入...")
    
    try:
        # 测试 train.py 导入
        print("  测试 train.py...")
        from train import train_main
        print("  ✅ train.py 导入成功")
        
        # 测试 test.py 导入
        print("  测试 test.py...")
        from test import test_main
        print("  ✅ test.py 导入成功")
        
        # 测试 run.py 导入
        print("  测试 run.py...")
        from run import DataPreprocessor, main
        print("  ✅ run.py 导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from run import DataPreprocessor
        import numpy as np
        import pandas as pd
        
        # 创建简单测试数据
        X = pd.DataFrame({
            'num1': [1, 2, 3, 4, 5],
            'num2': [2, 4, 6, 8, 10],
            'cat1': ['A', 'B', 'A', 'C', 'B']
        })
        y = np.array([0, 1, 0, 1, 0])
        
        print("  创建预处理器...")
        preprocessor = DataPreprocessor(
            enable_smote=False,  # 禁用SMOTE以简化测试
            enable_pca=False,    # 禁用PCA以简化测试
            random_state=42
        )
        
        print("  检测分类特征...")
        cat_features = preprocessor.detect_categorical_features(X)
        print(f"  检测到分类特征: {cat_features}")
        
        print("  ✅ 基本功能测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 快速修复验证")
    print("=" * 30)
    
    # 1. 测试导入
    import_ok = test_imports()
    
    # 2. 测试基本功能
    func_ok = test_basic_functionality()
    
    # 总结
    print(f"\n📊 测试结果:")
    print(f"  导入测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"  功能测试: {'✅ 通过' if func_ok else '❌ 失败'}")
    
    if import_ok and func_ok:
        print(f"\n🎉 修复成功！可以正常使用了")
        print(f"\n💡 使用方法:")
        print(f"  python run.py --enable_smote --enable_pca")
        return True
    else:
        print(f"\n⚠️ 仍有问题需要解决")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
