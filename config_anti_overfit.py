# -*- coding: utf-8 -*-
"""
config_anti_overfit.py
---------------------
防过拟合的配置文件
"""

from pathlib import Path

# 项目根路径
PROJECT_ROOT = Path(__file__).resolve().parent

config = {
    # ========= 全局 =========
    "global": {
        "seed": 42,
        "class_weight": {0: 1.0, 1: 5.0, 2: 2.0, 3: 1.5},  # 降低类别1权重，防止过拟合
        "verbose_data_info": True,
    },

    # ========= 数据路径 =========
    "paths": {
        "train_data": str(PROJECT_ROOT / "data_2" / "train_data.csv"),
        "val_data": str(PROJECT_ROOT / "data_2" / "val_data.csv"),
        "test_data": str(PROJECT_ROOT / "data_2" / "test_data.csv"),

        # 模型保存
        "xgb_model_path": str(PROJECT_ROOT / "results" / "xgb_model.json"),
        "cat_model_path": str(PROJECT_ROOT / "results" / "cat_model.cbm"),
        "stacking_model": str(PROJECT_ROOT / "results" / "stacking_model.pkl"),
        "class_weights": str(PROJECT_ROOT / "results" / "class_weights.pkl"),
        "result_dir": str(PROJECT_ROOT / "results"),
    },

    # ========= 任务信息 =========
    "task": {
        "label_col": "RISK_LEVEL",
        "num_class": None,  # None -> 自动
    },

    # ========= 防过拟合的XGBoost配置 =========
    "xgb": {
        "objective": "multi:softprob",
        "num_class": None,
        "eval_metric": "mlogloss",
        "random_state": 42,
        
        # 🛡️ 防过拟合参数
        "n_estimators": 500,        # 减少树的数量
        "max_depth": 4,             # 降低树的深度
        "learning_rate": 0.05,      # 适中的学习率
        "subsample": 0.7,           # 降低采样率
        "colsample_bytree": 0.7,    # 降低特征采样率
        "colsample_bylevel": 0.7,   # 每层特征采样
        "colsample_bynode": 0.7,    # 每个节点特征采样
        
        # 🔧 正则化参数
        "gamma": 2.0,               # 增加最小分裂损失
        "reg_alpha": 3.0,           # L1正则化
        "reg_lambda": 5.0,          # L2正则化
        "min_child_weight": 5,      # 增加叶子节点最小权重
        
        # 🎯 早停和验证
        "early_stopping_rounds": 50,
        "tree_method": "hist",      # 使用CPU，更稳定
        "max_delta_step": 0,        # 保守的步长
    },

    # ========= 防过拟合的CatBoost配置 =========
    "cat": {
        "loss_function": "MultiClass",
        "eval_metric": "MultiClass",
        "random_seed": 42,
        "verbose": False,
        
        # 🛡️ 防过拟合参数
        "iterations": 800,          # 减少迭代次数
        "depth": 4,                 # 降低深度
        "learning_rate": 0.05,      # 适中的学习率
        
        # 🔧 正则化参数
        "l2_leaf_reg": 10.0,        # 增加L2正则化
        "random_strength": 2.0,     # 增加随机性
        "bagging_temperature": 0.5, # 降低bagging温度
        "border_count": 32,         # 减少边界数量
        
        # 🎯 采样和验证
        "subsample": 0.7,           # 降低采样率
        "bootstrap_type": "Bernoulli",
        "early_stopping_rounds": 50,
        "task_type": "CPU",         # 使用CPU，更稳定
        
        # 类别权重在训练时动态设置
    },

    # ========= 保守的堆叠配置 =========
    "ensemble": {
        "use_stacking": True,
        "meta_learner": "lightgbm",
        "cv_folds": 5,              # 5折交叉验证
        "random_state": 42,
        
        # 🛡️ 防过拟合的元学习器参数
        "meta_params": {
            "n_estimators": 100,    # 较少的树
            "max_depth": 3,         # 浅层树
            "learning_rate": 0.1,   # 适中学习率
            "subsample": 0.8,
            "colsample_bytree": 0.8,
            "reg_alpha": 1.0,
            "reg_lambda": 1.0,
            "random_state": 42
        }
    },

    # ========= 数据预处理配置 =========
    "preprocessing": {
        "enable_smote": True,
        "enable_pca": True,
        "pca_components": 0.90,     # 保留90%方差，降低维度
        
        # 🛡️ 保守的SMOTE配置
        "smote_strategy": "minority",  # 只对少数类过采样
        "smote_k_neighbors": 3,        # 减少邻居数
        "smote_random_state": 42,
    },

    # ========= 验证和评估 =========
    "validation": {
        "cv_folds": 5,
        "stratify": True,
        "shuffle": True,
        "random_state": 42,
        
        # 🎯 性能监控
        "monitor_overfitting": True,
        "train_val_gap_threshold": 0.1,  # 训练验证差距阈值
        "early_stopping_patience": 3,
    },

    # ========= 调试和诊断 =========
    "debug": {
        "save_predictions": True,
        "save_feature_importance": True,
        "plot_learning_curves": True,
        "analyze_overfitting": True,
    }
}
