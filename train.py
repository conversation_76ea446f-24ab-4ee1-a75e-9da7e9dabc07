#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
train.py
--------
使用 config 中的设置训练：
  1. 载入训练 / 验证数据
  2. 类别权重
  3. 训练 XGB + CatBoost
  4. 构造叶序列并训练 LeafTransformer (Performer)
  5. 验证集搜索融合权重
  6. 保存全部模型与融合信息
"""

from __future__ import annotations
import json, logging, time, os
from pathlib import Path
from typing import Dict, Any, Tuple

import numpy as np
import tensorflow as tf
from tensorflow.keras import mixed_precision

from config import config, sync_num_class
from tree_attention_pipeline import (
    seed_all, maybe_infer_val_split, infer_class_weight,
    train_xgb, train_cat, train_stacking_ensemble, save_all,
    StackingFusionWrapper, SimpleStackingWrapper
)
from sklearn.metrics import f1_score

LOGGER = logging.getLogger(__name__)
if not LOGGER.handlers:
    logging.basicConfig(level=logging.INFO,
                        format="%(asctime)s - %(levelname)s - %(message)s")


def train_xgb_with_monitoring(X_train, y_train, X_val, y_val, cls_w, xgb_config):
    """训练XGBoost并监控过拟合"""
    # 训练模型
    model = train_xgb(X_train, y_train, cls_w, xgb_config)

    # 评估训练集和验证集性能
    train_pred = model.predict(X_train)
    val_pred = model.predict(X_val)

    train_score = f1_score(y_train, train_pred, average='macro')
    val_score = f1_score(y_val, val_pred, average='macro')

    return model, train_score, val_score


def train_cat_with_monitoring(X_train, y_train, X_val, y_val, cls_w, cat_config):
    """训练CatBoost并监控过拟合"""
    # 训练模型
    model = train_cat(X_train, y_train, cls_w, cat_config)

    # 评估训练集和验证集性能
    train_pred = model.predict(X_train)
    val_pred = model.predict(X_val)

    train_score = f1_score(y_train, train_pred, average='macro')
    val_score = f1_score(y_val, val_pred, average='macro')

    return model, train_score, val_score

# 完全禁用混合精度，使用纯float32
mixed_precision.set_global_policy('float32')


# 注释掉这行: mixed_precision.set_global_policy('mixed_bfloat16')

# ---------- Warm-up 回调：前 1 000 step 把 LR 从 0 线性升到 lr_base ----------
class WarmUp(tf.keras.callbacks.Callback):
    def __init__(self, lr_base, steps=3000):  # 更长的warmup
        super().__init__()
        self.lr_base = float(lr_base)
        self.steps = int(steps)

    def on_train_batch_begin(self, batch, logs=None):
        gstep = self.model.optimizer.iterations.numpy()
        if gstep < self.steps:
            # 从很小的学习率开始
            lr = self.lr_base * (gstep + 1) / self.steps * 0.1
            tf.keras.backend.set_value(self.model.optimizer.lr, lr)


# =====================================================================================
# Focal CCE
# =====================================================================================
def make_focal_cce(alpha, gamma, label_smoothing=0.0):
    alpha = tf.constant(alpha, dtype=tf.float32)
    gamma = tf.constant(gamma, dtype=tf.float32)

    def _loss(y_true, y_pred):
        if label_smoothing and label_smoothing > 0:
            n = tf.shape(y_true)[-1]
            y_true = (1 - label_smoothing) * y_true + label_smoothing / tf.cast(n, y_true.dtype)
        y_pred = tf.clip_by_value(y_pred, 1e-8, 1.0 - 1e-8)
        ce = -tf.reduce_sum(y_true * tf.math.log(y_pred), axis=-1)
        p_t = tf.reduce_sum(y_true * y_pred, axis=-1)
        focal = tf.pow(1. - p_t, gamma)
        a = tf.reduce_sum(y_true * alpha, axis=-1)
        return a * focal * ce

    return _loss


class NaNDetector(tf.keras.callbacks.Callback):
    def __init__(self, logger):
        super().__init__()
        self.logger = logger

    def on_train_batch_end(self, batch, logs=None):
        loss = logs.get("loss")
        if loss is None:
            return
        if np.isnan(loss) or np.isinf(loss):
            self.logger.error("❌ Batch %d loss=%.3e 出现 NaN/Inf", batch, loss)
            print(f"❌ Loss 出现非法值：第 {batch} 个 batch，loss={loss}")
            self.model.stop_training = True


class TrainingMonitor(tf.keras.callbacks.Callback):
    def __init__(self, save_dir):
        super().__init__()
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        self.epoch_data = []
        self.batch_data = []

    def on_epoch_begin(self, epoch, logs=None):
        self.current_epoch = epoch
        self.epoch_start_time = time.time()
        self.batch_data = []  # 重置当前轮次的batch数据

    def on_batch_end(self, batch, logs=None):
        if logs:
            # 转换TensorFlow类型为Python原生类型
            converted_logs = {}
            for key, value in logs.items():
                if hasattr(value, 'numpy'):
                    converted_logs[key] = float(value.numpy())
                else:
                    converted_logs[key] = float(value)

            batch_info = {
                'epoch': self.current_epoch,
                'batch': batch,
                'timestamp': time.time(),
                **converted_logs
            }
            self.batch_data.append(batch_info)

    def on_epoch_end(self, epoch, logs=None):
        epoch_time = time.time() - self.epoch_start_time

        # 转换TensorFlow类型为Python原生类型
        if logs:
            converted_logs = {}
            for key, value in logs.items():
                if hasattr(value, 'numpy'):
                    converted_logs[key] = float(value.numpy())
                else:
                    converted_logs[key] = float(value)
        else:
            converted_logs = {}

        # 保存当前轮次的详细数据
        epoch_file = self.save_dir / f"epoch_{epoch:03d}_details.json"
        epoch_summary = {
            'epoch': epoch,
            'duration': epoch_time,
            'summary_metrics': converted_logs,  # 使用转换后的logs
            'batch_details': self.batch_data
        }

        with open(epoch_file, 'w', encoding='utf-8') as f:
            json.dump(epoch_summary, f, indent=2, ensure_ascii=False)

        # 累积到总体数据
        self.epoch_data.append({
            'epoch': epoch,
            'duration': epoch_time,
            **converted_logs  # 使用转换后的logs
        })

        LOGGER.info(f"📊 轮次 {epoch} 监控数据已保存到 {epoch_file}")

    def on_train_end(self, logs=None):
        # 保存整体训练摘要
        summary_file = self.save_dir / "training_summary.json"
        summary = {
            'total_epochs': len(self.epoch_data),
            'total_duration': sum(e['duration'] for e in self.epoch_data),
            'epoch_summaries': self.epoch_data
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        LOGGER.info(f"📈 训练摘要已保存到 {summary_file}")


class ClassRecallCallback(tf.keras.callbacks.Callback):
    def __init__(self, val_data, num_classes=4, dynamic_weight_callback=None):
        super().__init__()
        self.val_data = val_data
        self.num_classes = num_classes
        self.dynamic_weight_callback = dynamic_weight_callback
        self.recall_history = []
        self.epoch_count = 0

    def on_epoch_end(self, epoch, logs=None):
        self.epoch_count = epoch

        # 收集验证数据的真实标签和预测结果
        y_true_list = []
        y_pred_list = []

        for batch in self.val_data:
            if len(batch) == 3:
                x_batch, y_batch, _ = batch
            else:
                x_batch, y_batch = batch

            # 预测
            y_pred_batch = self.model(x_batch, training=False)

            # 转换为类别标签
            y_true_batch = tf.argmax(y_batch, axis=1)
            y_pred_batch = tf.argmax(y_pred_batch, axis=1)

            y_true_list.append(y_true_batch.numpy())
            y_pred_list.append(y_pred_batch.numpy())

        # 合并所有批次
        y_true = np.concatenate(y_true_list)
        y_pred = np.concatenate(y_pred_list)

        # 计算每个类别的recall
        recalls = []
        precisions = []
        f1_scores = []

        for i in range(self.num_classes):
            # Recall计算
            true_positives = np.sum((y_true == i) & (y_pred == i))
            actual_positives = np.sum(y_true == i)
            recall = true_positives / actual_positives if actual_positives > 0 else 0.0
            recalls.append(recall)

            # Precision计算
            predicted_positives = np.sum(y_pred == i)
            precision = true_positives / predicted_positives if predicted_positives > 0 else 0.0
            precisions.append(precision)

            # F1计算
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
            f1_scores.append(f1)

        self.recall_history.append(recalls)

        # 详细输出结果
        print(f"\n📊 Epoch {epoch + 1} 详细性能分析:")
        print("=" * 60)

        all_good = all(r >= 0.8 for r in recalls)
        min_recall = min(recalls)
        max_recall = max(recalls)

        print(f"📈 召回率总览: 最低={min_recall:.3f}, 最高={max_recall:.3f}, 全部≥0.8: {'✅' if all_good else '❌'}")

        for i, (recall, precision, f1) in enumerate(zip(recalls, precisions, f1_scores)):
            # 状态图标
            if recall >= 0.9:
                if all_good:
                    status = "🌟"  # 优秀且平衡
                else:
                    status = "⚠️"  # 过高但不平衡
            elif recall >= 0.8:
                status = "✅"
            elif recall >= 0.5:
                status = "🟡"
            elif recall >= 0.1:
                status = "🟠"
            else:
                status = "❌"

            print(f"  类别{i}: R={recall:.3f} P={precision:.3f} F1={f1:.3f} {status}")

        # 宏平均指标
        macro_recall = np.mean(recalls)
        macro_precision = np.mean(precisions)
        macro_f1 = np.mean(f1_scores)

        print(f"\n📊 宏平均: R={macro_recall:.3f} P={macro_precision:.3f} F1={macro_f1:.3f}")

        # 预测分布分析
        pred_counts = np.bincount(y_pred, minlength=self.num_classes)
        true_counts = np.bincount(y_true, minlength=self.num_classes)

        print(f"\n📈 分布分析:")
        print(f"  真实分布: {true_counts}")
        print(f"  预测分布: {pred_counts}")

        # 分析预测偏差和给出建议
        print(f"\n🔍 偏差分析:")
        for i in range(self.num_classes):
            if true_counts[i] > 0:
                bias = pred_counts[i] / true_counts[i]
                if bias < 0.3:
                    print(f"  类别{i}: 严重预测不足 (偏差={bias:.2f}) 🚨")
                elif bias < 0.7:
                    print(f"  类别{i}: 预测不足 (偏差={bias:.2f}) ⚠️")
                elif bias > 3.0:
                    print(f"  类别{i}: 严重预测过多 (偏差={bias:.2f}) 🚨")
                elif bias > 1.5:
                    print(f"  类别{i}: 预测过多 (偏差={bias:.2f}) ⚠️")
                else:
                    print(f"  类别{i}: 预测平衡 (偏差={bias:.2f}) ✅")

        # 通知动态权重调整回调
        if self.dynamic_weight_callback:
            self.dynamic_weight_callback.update_recall_data(recalls)

        # 训练进度评估
        self._assess_training_progress(epoch, recalls)

    def _assess_training_progress(self, epoch, recalls):
        """评估训练进度"""
        if epoch < 5:
            return  # 前几个epoch不评估

        print(f"\n🎯 训练进度评估:")

        # 检查是否有改善
        if len(self.recall_history) >= 3:
            recent_recalls = self.recall_history[-3:]
            improvements = []

            for class_id in range(self.num_classes):
                class_recalls = [r[class_id] for r in recent_recalls]
                trend = class_recalls[-1] - class_recalls[0]  # 3个epoch的变化
                improvements.append(trend)

                if trend > 0.1:
                    print(f"  类别{class_id}: 显著改善 (+{trend:.3f}) 📈")
                elif trend > 0.05:
                    print(f"  类别{class_id}: 轻微改善 (+{trend:.3f}) 📊")
                elif trend < -0.1:
                    print(f"  类别{class_id}: 显著下降 ({trend:.3f}) 📉")
                elif trend < -0.05:
                    print(f"  类别{class_id}: 轻微下降 ({trend:.3f}) 📊")
                else:
                    print(f"  类别{class_id}: 基本稳定 ({trend:+.3f}) ➡️")

        # 给出下一步建议
        min_recall = min(recalls)
        if min_recall == 0.0:
            print(f"\n💡 下一步建议: 仍有类别完全无召回，继续增加权重")
        elif min_recall < 0.3:
            print(f"\n💡 下一步建议: 最低召回率{min_recall:.3f}，需要继续调整")
        elif min_recall < 0.6:
            print(f"\n💡 下一步建议: 进展良好，保持当前策略")
        else:
            print(f"\n💡 下一步建议: 表现优秀，可以考虑精调")


class StrictDynamicWeightCallback(tf.keras.callbacks.Callback):
    def __init__(self, initial_weights, adjustment_factor=1.3, patience=2):
        super().__init__()
        self.initial_weights = initial_weights.copy()
        self.current_weights = initial_weights.copy()
        self.adjustment_factor = adjustment_factor
        self.patience = patience
        self.poor_performance_count = {i: 0 for i in range(len(initial_weights))}
        self.recall_threshold_low = 0.05  # 降低阈值，更早触发调整
        self.recall_threshold_high = 0.8
        self.recall_threshold_excellent = 0.9
        self.zero_recall_multiplier = 3.0  # 专门针对0召回率的强力调整

    def update_recall_data(self, recalls):
        """由ClassRecallCallback调用，传入当前recall数据"""
        self.recall_data = recalls
        self._adjust_weights_with_strict_logic()

    def _adjust_weights_with_strict_logic(self):
        """严格的权重调整逻辑 - 专门处理0召回率"""
        adjusted = False
        min_recall = min(self.recall_data)

        # 检查所有类别是否都达标
        all_classes_good = all(r >= self.recall_threshold_high for r in self.recall_data)

        print(f"\n🔍 权重调整分析:")
        print(f"  最低召回率: {min_recall:.3f}")

        for class_id, recall in enumerate(self.recall_data):
            old_weight = self.current_weights[class_id]

            # 🚨 特殊处理：召回率为0的类别
            if recall == 0.0:
                # 大幅增加权重
                self.current_weights[class_id] *= self.zero_recall_multiplier
                print(
                    f"  🚨 类别{class_id}: {old_weight:.2f} → {self.current_weights[class_id]:.2f} (召回率为0，紧急调整)")
                adjusted = True

            # 召回率极低但不为0
            elif recall < self.recall_threshold_low:
                self.poor_performance_count[class_id] += 1

                if self.poor_performance_count[class_id] >= self.patience:
                    multiplier = 2.5 if recall < 0.01 else 2.0
                    self.current_weights[class_id] *= multiplier
                    adjusted = True
                    self.poor_performance_count[class_id] = 0

            # 2. 召回率过高但其他类别未达标：降低权重
            elif recall >= self.recall_threshold_excellent and not all_classes_good:
                # 只有当其他类别都达到0.8时，才允许某个类别保持0.9+
                self.current_weights[class_id] *= 0.85  # 降低权重
                adjusted = True

            # 3. 召回率在0.8-0.9之间且其他类别未达标：轻微降低
            elif recall >= self.recall_threshold_high and recall < self.recall_threshold_excellent and not all_classes_good:
                # 如果有其他类别表现很差，适度降低这个类别的权重
                worst_recall = min(self.recall_data)
                if worst_recall < 0.3:  # 有类别表现很差
                    self.current_weights[class_id] *= 0.95
                    adjusted = True

            # 4. 表现良好：重置计数
            elif recall >= self.recall_threshold_low:
                self.poor_performance_count[class_id] = 0

                # 如果所有类别都达标，可以轻微降低权重避免过拟合
                if all_classes_good and recall > 0.95:
                    self.current_weights[class_id] *= 0.98
                    adjusted = True

        # 🎯 特殊检查：如果类别3仍然为0，强制调整
        if 3 < len(self.recall_data) and self.recall_data[3] == 0.0:
            if self.current_weights[3] < 10.0:  # 如果权重还不够高
                self.current_weights[3] = max(self.current_weights[3] * 2.0, 10.0)
                adjusted = True

        if adjusted:
            self._update_model_weights()

    def _rebalance_extreme_weights(self):
        """重平衡极端权重"""
        # 限制最大权重不超过最小权重的8倍
        min_weight = min(self.current_weights)
        max_allowed = min_weight * 8

        for i in range(len(self.current_weights)):
            if self.current_weights[i] > max_allowed:
                old_weight = self.current_weights[i]
                self.current_weights[i] = max_allowed
                # print(f"    类别{i}权重限制: {old_weight:.2f} → {self.current_weights[i]:.2f}")

    def _update_model_weights(self):
        """更新模型的类别权重"""
        # print(f"📊 更新后类别权重: {dict(enumerate([f'{w:.2f}' for w in self.current_weights]))}")

        # 计算权重变化幅度
        weight_changes = []
        for i, (old, new) in enumerate(zip(self.initial_weights, self.current_weights)):
            change = (new - old) / old * 100
            weight_changes.append(change)
            if abs(change) > 20:  # 变化超过20%
                direction = "↗️" if change > 0 else "↘️"
                # print(f"  类别{i}权重变化: {change:+.1f}% {direction}")

        # 给出训练建议
        self._provide_training_advice()

    def _provide_training_advice(self):
        """基于当前状态给出训练建议"""
        recalls = self.recall_data
        min_recall = min(recalls)
        max_recall = max(recalls)
        recall_std = np.std(recalls)

        print(f"\n💡 训练建议:")

        '''if min_recall < 0.1:
            worst_classes = [i for i, r in enumerate(recalls) if r < 0.1]
            print(f"  🚨 类别{worst_classes}表现极差，考虑:")
            print(f"     - 检查数据质量和标签正确性")
            print(f"     - 增加这些类别的训练样本")
            print(f"     - 调整模型架构或超参数")

        elif recall_std > 0.3:
            print(f"  ⚠️ 类别间表现差异较大(std={recall_std:.3f})，建议:")
            print(f"     - 继续当前的动态权重调整")
            print(f"     - 考虑使用不同的损失函数")

        elif min_recall > 0.6:
            print(f"  ✅ 整体表现良好，建议:")
            print(f"     - 保持当前训练策略")
            print(f"     - 可以考虑降低学习率精调")

        else:
            print(f"  📈 训练进展正常，继续观察")'''


# =====================================================================================
# 主训练函数
# =====================================================================================
def train_main_ultimate(cfg: Dict[str, Any] | None = None):
    """终极训练主函数 - 整合所有优化"""
    if cfg is None:
        cfg = config

    print("🚀 启动终极训练模式")
    print("=" * 60)

    # 禁用混合精度 - 避免梯度爆炸
    if cfg.get("ultimate", {}).get("mixed_precision", False):  # 改为False
        policy = mixed_precision.Policy('mixed_float16')
        mixed_precision.set_global_policy(policy)
        print("✅ 混合精度训练已启用")
    else:
        print("✅ 使用标准精度训练（避免梯度爆炸）")

    seed_all(cfg["global"]["seed"])

    paths = cfg["paths"]
    label_col = cfg["task"]["label_col"]
    attn_cfg = cfg["attn"]

    # 检查是否有预处理后的数据
    if "processed_train_data" in paths and os.path.exists(paths["processed_train_data"]):
        print("📂 使用预处理后的数据...")

        # 加载预处理后的数据
        train_data = np.load(paths["processed_train_data"])
        val_data = np.load(paths["processed_val_data"])

        X_tr, y_tr = train_data['X'], train_data['y']
        X_va, y_va = val_data['X'], val_data['y']

        # 生成特征列名
        feat_cols = [f"feature_{i}" for i in range(X_tr.shape[1])]

        print("✅ 预处理数据加载完成")
    else:
        print("📂 使用原始数据...")
        # 读取原始数据
        (X_tr, y_tr), (X_va, y_va), feat_cols = maybe_infer_val_split(
            paths["train_data"], paths["val_data"], label_col,
            val_size=0.2, seed=cfg["global"]["seed"])

    num_cls = int(cfg["task"]["num_class"] or len(np.unique(y_tr)))
    cfg = sync_num_class(cfg, num_cls)

    print(f"📊 数据统计:")
    print(f"   训练集: {X_tr.shape}, 验证集: {X_va.shape}")
    print(f"   特征数: {X_tr.shape[1]}, 类别数: {num_cls}")
    print(f"   类别分布: {dict(zip(*np.unique(y_tr, return_counts=True)))}")

    # 🎯 终极类别权重计算 - 特别关注类别1
    cls_w = infer_class_weight(y_tr, cfg["global"]["class_weight"])

    # 🔍 过拟合风险评估
    print(f"\n🔍 过拟合风险评估:")
    train_val_ratio = len(X_tr) / len(X_va) if len(X_va) > 0 else float('inf')
    print(f"   训练/验证比例: {train_val_ratio:.2f}")

    if train_val_ratio > 4:
        print(f"   ⚠️ 验证集可能过小，建议增加验证集比例")
    elif train_val_ratio < 2:
        print(f"   ⚠️ 训练集可能过小，可能影响模型性能")
    else:
        print(f"   ✅ 训练/验证比例合理")

    # 🔍 过拟合风险评估
    print(f"\n🔍 过拟合风险评估:")
    train_val_ratio = len(X_tr) / len(X_va) if len(X_va) > 0 else float('inf')
    print(f"   训练/验证比例: {train_val_ratio:.2f}")

    if train_val_ratio > 4:
        print(f"   ⚠️ 验证集可能过小，建议增加验证集比例")
    elif train_val_ratio < 2:
        print(f"   ⚠️ 训练集可能过小，可能影响模型性能")
    else:
        print(f"   ✅ 训练/验证比例合理")

    # 🎯 特殊增强类别1（解决召回率低的问题）
    if isinstance(cls_w, dict):
        cls_w[1] = cls_w.get(1, 1.0) * 3.0  # 类别1额外3倍权重
        print(f"🎯 优化后类别权重 (重点提升类别1): {cls_w}")

        # 分析类别分布
        unique, counts = np.unique(y_tr, return_counts=True)
        class_dist = dict(zip(unique, counts))
        print(f"📊 训练集类别分布: {class_dist}")

        # 计算类别1的比例
        class_1_ratio = class_dist.get(1, 0) / len(y_tr)
        print(f"🎯 类别1占比: {class_1_ratio:.3f} ({'少数类' if class_1_ratio < 0.3 else '正常'})")

    # ================= 🚀 终极树模型训练 =================
    print("\n🌳 开始终极树模型训练")
    print("-" * 40)

    # XGBoost with validation monitoring
    print("🚀 训练XGBoost (带过拟合监控)...")
    xgb_model, xgb_train_score, xgb_val_score = train_xgb_with_monitoring(
        X_tr, y_tr, X_va, y_va, cls_w, cfg["xgb"]
    )

    # CatBoost with validation monitoring
    print("🚀 训练CatBoost (带过拟合监控)...")
    cat_model, cat_train_score, cat_val_score = train_cat_with_monitoring(
        X_tr, y_tr, X_va, y_va, cls_w, cfg["cat"]
    )

    # 🔍 过拟合检测
    print(f"\n🔍 过拟合检测:")
    xgb_gap = xgb_train_score - xgb_val_score
    cat_gap = cat_train_score - cat_val_score

    print(f"   XGBoost - 训练: {xgb_train_score:.4f}, 验证: {xgb_val_score:.4f}, 差距: {xgb_gap:.4f}")
    print(f"   CatBoost - 训练: {cat_train_score:.4f}, 验证: {cat_val_score:.4f}, 差距: {cat_gap:.4f}")

    if xgb_gap > 0.1 or cat_gap > 0.1:
        print(f"   ⚠️ 检测到过拟合，建议使用防过拟合配置重新训练")
    else:
        print(f"   ✅ 模型拟合良好")

    # ================= 🎯 堆叠集成训练 =================
    print("\n🎯 开始训练堆叠集成模型")
    print("-" * 40)

    # 训练堆叠模型
    stacking_model = train_stacking_ensemble(
        X_tr, y_tr, xgb_model, cat_model,
        sample_weight=cls_w if isinstance(cls_w, np.ndarray) else None,
        cv_folds=5,
        random_state=cfg["global"]["seed"]
    )

    print(f"✅ 堆叠集成模型训练完成")
    # ================= 🎯 堆叠模型验证 =================
    print("\n🎯 验证堆叠模型性能")
    print("-" * 40)

    # 在验证集上评估堆叠模型
    val_proba = stacking_model.predict_proba(X_va)
    val_pred = np.argmax(val_proba, axis=1)

    from sklearn.metrics import classification_report, f1_score
    val_f1 = f1_score(y_va, val_pred, average='macro')

    print(f"✅ 堆叠模型验证集性能:")
    print(f"   Macro F1-Score: {val_f1:.4f}")
    print("\n📊 详细分类报告:")
    print(classification_report(y_va, val_pred, target_names=[f'Class_{i}' for i in range(num_cls)]))

    # ================= 💾 保存所有模型 =================
    print("\n💾 保存堆叠模型")
    print("-" * 40)

    save_all(xgb_model, cat_model, stacking_model, cls_w, paths)

    # 保存配置
    config_path = Path(paths["result_dir"]) / "stacking_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(cfg, f, indent=2, ensure_ascii=False, default=str)

    print("🏆 堆叠模型训练完成！")
    print("=" * 60)

    return xgb_model, cat_model, stacking_model, cls_w, cfg


# 替换原来的train_main
train_main = train_main_ultimate


def analyze_model_complementarity(y_true, p_xgb, p_cat, p_attn):
    """分析模型间的互补性"""
    pred_xgb = p_xgb.argmax(1)
    pred_cat = p_cat.argmax(1)
    pred_attn = p_attn.argmax(1)

    print("\n🔄 模型互补性分析:")
    print("=" * 40)

    # 计算模型间的一致性
    xgb_cat_agree = np.mean(pred_xgb == pred_cat)
    xgb_attn_agree = np.mean(pred_xgb == pred_attn)
    cat_attn_agree = np.mean(pred_cat == pred_attn)

    print(f"XGB-Cat一致性: {xgb_cat_agree:.3f}")
    print(f"XGB-Attn一致性: {xgb_attn_agree:.3f}")
    print(f"Cat-Attn一致性: {cat_attn_agree:.3f}")

    # 分析不同模型擅长的样本
    xgb_correct = (pred_xgb == y_true)
    cat_correct = (pred_cat == y_true)
    attn_correct = (pred_attn == y_true)

    # 只有某个模型预测正确的样本数
    only_xgb = xgb_correct & ~cat_correct & ~attn_correct
    only_cat = ~xgb_correct & cat_correct & ~attn_correct
    only_attn = ~xgb_correct & ~cat_correct & attn_correct

    print(f"\n独特贡献样本数:")
    print(f"  仅XGB正确: {only_xgb.sum()}")
    print(f"  仅Cat正确: {only_cat.sum()}")
    print(f"  仅Attn正确: {only_attn.sum()}")

    # 所有模型都错误的样本
    all_wrong = ~xgb_correct & ~cat_correct & ~attn_correct
    print(f"  全部错误: {all_wrong.sum()}")

    # 互补性评分 (越低越好，表示模型差异大)
    avg_agreement = (xgb_cat_agree + xgb_attn_agree + cat_attn_agree) / 3
    complementarity_score = 1 - avg_agreement

    print(f"\n互补性评分: {complementarity_score:.3f} (0-1, 越高越好)")

    if complementarity_score > 0.3:
        print("✅ 模型互补性良好")
    elif complementarity_score > 0.15:
        print("⚠️ 模型互补性一般")
    else:
        print("❌ 模型互补性较差，可能存在相似偏差")

    return complementarity_score


def predict_final_impact(baseline_perf, current_perf, fusion_weights):
    """预测基础模型下降对最终效果的影响"""

    print("\n🎯 最终效果影响预测:")
    print("=" * 40)

    # 基础模型性能变化
    xgb_change = current_perf['xgb'] - baseline_perf['xgb']
    cat_change = current_perf['cat'] - baseline_perf['cat']

    print(f"XGBoost性能变化: {xgb_change:+.3f}")
    print(f"CatBoost性能变化: {cat_change:+.3f}")

    # 根据融合权重估算影响
    weighted_impact = (fusion_weights[0] * xgb_change +
                       fusion_weights[1] * cat_change)

    print(f"加权影响估算: {weighted_impact:+.3f}")

    # 影响程度评估
    if abs(weighted_impact) < 0.01:
        impact_level = "轻微"
        color = "🟢"
    elif abs(weighted_impact) < 0.03:
        impact_level = "中等"
        color = "🟡"
    else:
        impact_level = "显著"
        color = "🔴"

    print(f"{color} 预计对最终效果的影响: {impact_level}")

    # 建议
    if weighted_impact < -0.02:
        print("\n💡 建议:")
        print("1. 优先修复性能下降较大的模型")
        print("2. 增加注意力模型的权重")
        print("3. 考虑引入新的基础模型")
    elif abs(weighted_impact) < 0.01:
        print("\n✅ 影响较小，融合机制有效补偿了基础模型的下降")

    return weighted_impact


if __name__ == "__main__":
    train_main()
