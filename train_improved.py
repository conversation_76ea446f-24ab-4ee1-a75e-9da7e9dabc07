#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
train_improved.py
----------------
使用改进配置训练模型，专门解决类别1召回率低的问题
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras import mixed_precision
import logging
from pathlib import Path

# 导入配置和训练函数
from config import config
from tree_attention_pipeline import (
    seed_all, maybe_infer_val_split, infer_class_weight,
    train_xgb, train_cat, train_stacking_ensemble,
    StackingFusionWrapper, save_all
)

# 设置日志
LOGGER = logging.getLogger(__name__)
if not LOGGER.handlers:
    logging.basicConfig(level=logging.INFO,
                        format="%(asctime)s - %(levelname)s - %(message)s")

# 使用纯float32，避免混合精度问题
mixed_precision.set_global_policy('float32')

def analyze_model_complementarity(y_true, p_xgb, p_cat, p_attn):
    """分析模型互补性"""
    from sklearn.metrics import accuracy_score
    
    # 计算各模型单独的准确率
    acc_xgb = accuracy_score(y_true, np.argmax(p_xgb, axis=1))
    acc_cat = accuracy_score(y_true, np.argmax(p_cat, axis=1))
    acc_attn = accuracy_score(y_true, np.argmax(p_attn, axis=1))
    
    # 计算平均互补性（简化版本）
    complementarity = (acc_xgb + acc_cat + acc_attn) / 3
    
    print(f"📊 各模型准确率: XGB={acc_xgb:.3f}, Cat={acc_cat:.3f}, Attn={acc_attn:.3f}")
    
    return complementarity

def train_with_improved_config():
    """使用改进配置训练模型"""
    print("🚀 开始使用改进配置训练模型")
    print("=" * 60)
    
    # 使用改进的配置
    cfg = config
    
    # 设置随机种子
    seed_all(cfg["global"]["seed"])
    
    paths = cfg["paths"]
    label_col = cfg["task"]["label_col"]
    attn_cfg = cfg["attn"]
    
    print("📊 配置信息:")
    print(f"   类别权重: {cfg['global']['class_weight']}")
    print(f"   Focal Alpha: {attn_cfg['focal_alpha']}")
    print(f"   模型容量: embed_dim={attn_cfg['embed_dim']}, layers={attn_cfg['num_layers']}")
    print(f"   训练轮数: {attn_cfg['epochs']}")
    
    # ================= 📂 数据加载 =================
    print("\n📂 加载数据...")
    (X_tr, y_tr), (X_va, y_va), feat_cols = maybe_infer_val_split(
        paths["train_data"], paths["val_data"], label_col,
        val_size=0.2, seed=cfg["global"]["seed"])
    
    # 同步类别数
    from config import sync_num_class
    num_cls = int(cfg["task"]["num_class"] or len(np.unique(y_tr)))
    cfg = sync_num_class(cfg, num_cls)
    
    print(f"📊 数据统计:")
    print(f"   训练集: {X_tr.shape}, 验证集: {X_va.shape}")
    print(f"   特征数: {X_tr.shape[1]}, 类别数: {num_cls}")
    
    # 分析类别分布
    unique, counts = np.unique(y_tr, return_counts=True)
    class_dist = dict(zip(unique, counts))
    print(f"   训练集类别分布: {class_dist}")
    
    # 计算类别权重
    cls_w = infer_class_weight(y_tr, cfg["global"]["class_weight"])
    print(f"🎯 应用的类别权重: {cls_w}")
    
    # ================= 🌳 树模型训练 =================
    print("\n🌳 训练树模型...")
    print("-" * 40)
    
    # XGBoost
    print("🚀 训练优化的XGBoost...")
    xgb_model = train_xgb(X_tr, y_tr, cls_w, cfg["xgb"], cfg.get("xgb_tune"))
    
    # CatBoost
    print("🚀 训练优化的CatBoost...")
    cat_model = train_cat(X_tr, y_tr, cls_w, cfg["cat"], cfg.get("cat_tune"))
    
    # ================= 🎯 Token构建 =================
    print("\n🎯 构建智能token序列...")
    print("-" * 40)
    
    max_tok = attn_cfg.get("max_tokens", 1024)
    print(f"🎯 使用最大token长度: {max_tok}")
    
    # 构建tokens
    leaf_tr, depth_tr = build_tokens_ultimate(
        xgb_model, cat_model, X_tr, y_tr, max_tokens=max_tok,
        strategy=attn_cfg.get("token_selection_strategy", "class_aware"),
        config=attn_cfg.get("token_selection", {})
    )
    
    leaf_va, depth_va = build_tokens_ultimate(
        xgb_model, cat_model, X_va, y_va, max_tokens=max_tok,
        strategy=attn_cfg.get("token_selection_strategy", "class_aware"),
        config=attn_cfg.get("token_selection", {})
    )
    
    print(f"✅ Token构建完成:")
    print(f"   训练: leaf={leaf_tr.shape}, depth={depth_tr.shape}")
    print(f"   验证: leaf={leaf_va.shape}, depth={depth_va.shape}")
    
    # ================= 🚀 注意力模型训练 =================
    print("\n🚀 训练增强的注意力模型...")
    print("-" * 40)
    
    # 模型参数
    model_params = {k: v for k, v in attn_cfg.items() 
                    if k not in ['epochs', 'batch_size', 'token_selection_strategy',
                                'token_selection', 'use_focal', 'focal_alpha', 'focal_gamma',
                                'class_1_boost', 'class_1_boost_factor', 'class_3_boost',
                                'class_3_boost_factor', 'use_class_aware_loss', 'minority_boost',
                                'adaptive_lr', 'lr_scheduler', 'max_tokens', 'use_label_smoothing',
                                'label_smoothing', 'use_mixup', 'mixup_alpha']}
    
    # 创建模型
    attn_model = EnhancedLeafTransformer(
        num_classes=num_cls,
        vocab_leaf=int(leaf_tr.max()) + 2,
        vocab_depth=int(depth_tr.max()) + 2,
        **model_params
    )
    
    print(f"📋 模型参数:")
    print(f"   词汇表大小: leaf={int(leaf_tr.max()) + 2}, depth={int(depth_tr.max()) + 2}")
    print(f"   模型容量: {model_params}")
    
    # 训练模型
    attn_model = train_single_attention_model(
        attn_model, leaf_tr, depth_tr, y_tr, leaf_va, depth_va, y_va, cfg
    )
    
    # ================= 🎯 融合权重优化 =================
    print("\n⚖️ 优化融合权重...")
    print("-" * 40)
    
    if cfg["ensemble"]["optimize"]:
        # 获取各模型预测
        p_xgb = xgb_model.predict_proba(X_va)
        p_cat = cat_model.predict_proba(X_va)
        p_attn = attn_model.predict(
            {"leaf_ids": leaf_va, "depths": depth_va},
            batch_size=attn_cfg["batch_size"], verbose=0
        )
        
        # 优化融合权重
        w = auto_fusion_weights(
            y_va, p_xgb, p_cat, p_attn,
            n_sample=cfg["ensemble"]["n_dirichlet"],
            seed=cfg["global"]["seed"],
            local_refine=cfg["ensemble"]["local_refine"]
        )
        
        print(f"🎯 优化后的融合权重: {w}")
        
        # 分析模型互补性
        complementarity = analyze_model_complementarity(y_va, p_xgb, p_cat, p_attn)
        print(f"📊 模型互补性评分: {complementarity:.3f}")
        
    else:
        w = np.array([1/3, 1/3, 1/3], np.float32)
        print(f"🎯 使用均匀融合权重: {w}")
    
    # ================= 💾 保存模型 =================
    print("\n💾 保存所有模型...")
    print("-" * 40)
    
    save_all(xgb_model, cat_model, attn_model, w, cls_w, paths)
    
    print("🎉 训练完成！")
    print(f"📁 模型已保存到: {paths['result_dir']}")
    
    return xgb_model, cat_model, attn_model, w, cls_w

if __name__ == "__main__":
    print("🚀 开始改进模型训练")
    print("目标：提高类别1的召回率（当前仅39%）")
    print("=" * 60)
    
    try:
        # 训练改进模型
        models = train_with_improved_config()
        
        print("\n✅ 训练成功完成！")
        print("\n📋 下一步建议:")
        print("1. 运行 test.py 评估改进后的模型性能")
        print("2. 特别关注类别1的召回率是否有提升")
        print("3. 如果类别1召回率仍然不足，考虑进一步调整权重")
        print("4. 分析混淆矩阵，了解类别1主要被误分类到哪些类别")
        
    except Exception as e:
        print(f"❌ 训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
